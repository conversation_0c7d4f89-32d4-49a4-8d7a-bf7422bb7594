<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\provinceModel;
use App\Models\countryModel;

class Api extends ResourceController
{
    protected $format = 'json';

    public function get_provinces()
    {
        try {
            $country_id = $this->request->getPost('country_id');
            
            $provinceModel = new provinceModel();
            $query = $provinceModel->orderBy('name', 'ASC');
            
            if ($country_id) {
                $query->where('country_id', $country_id);
            }

            $provinces = $query->findAll();

            return $this->response->setJSON([
                'status' => 'success',
                'data' => $provinces
            ]);
        } catch (\Exception $e) {
            log_message('error', '[API Get Provinces] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching provinces'
            ]);
        }
    }

    public function get_countries()
    {
        try {
            $countries = model(countryModel::class)
                ->orderBy('name', 'ASC')
                ->findAll();

            return $this->response->setJSON([
                'status' => 'success',
                'data' => $countries
            ]);
        } catch (\Exception $e) {
            log_message('error', '[API Get Countries] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching countries'
            ]);
        }
    }

    public function get_districts()
    {
        try {
            $province_id = $this->request->getPost('province_id');
            
            if (!$province_id) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Province ID is required'
                ]);
            }

            $districts = model(districtModel::class)
                ->where('province_id', $province_id)
                ->orderBy('name', 'ASC')
                ->findAll();

            return $this->response->setJSON($districts);
        } catch (\Exception $e) {
            log_message('error', '[API Get Districts] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching districts'
            ]);
        }
    }

    public function get_llgs()
    {
        try {
            $district_id = $this->request->getPost('district_id');
            
            if (!$district_id) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'District ID is required'
                ]);
            }

            $llgs = model(llgModel::class)
                ->where('district_id', $district_id)
                ->orderBy('name', 'ASC')
                ->findAll();

            return $this->response->setJSON($llgs);
        } catch (\Exception $e) {
            log_message('error', '[API Get LLGs] ' . $e->getMessage());
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Error fetching LLGs'
            ]);
        }
    }
} 