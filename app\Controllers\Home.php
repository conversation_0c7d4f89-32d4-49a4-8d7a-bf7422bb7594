<?php

namespace App\Controllers;

use App\Models\districtModel;
use App\Models\employeesModel;
use App\Models\orgModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\projectsModel;
use App\Models\provinceModel;
use App\Models\usersModel;

class Home extends BaseController
{
    public $session;
    public $usersModel;
    public $orgModel;
    public $provinceModel;
    public $districtModel;




    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();

    }

    public function index()
    {
        $data = [
            'title' => 'Welcome'
        ];
        return view('home/home', $data);
    }



    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }

    public function login()
    {
        if (!$this->request->getPost('username') || !$this->request->getPost('password')) {
            return redirect()->back()->with('error', 'Username and password are required');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        $user = $this->usersModel->where('username', $username)->first();

        if ($user && password_verify($password, $user['password'])) {
            if ($user['status'] != 1) {
                return redirect()->back()->with('error', 'Your account is inactive. Please contact administrator.');
            }

            $this->session->set([
                'logged_in' => true,
                'user_id' => $user['id'],
                'name' => $user['name'],
                'role' => $user['role'],
                'org_id' => $user['org_id']
            ]);

            return redirect()->to('dashboard')->with('success', "Welcome back, {$user['name']}! You've successfully logged in.");
        }

        return redirect()->back()->with('error', 'Invalid username or password. Please try again.');
    }
}
