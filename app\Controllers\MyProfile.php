<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\employeesModel;

class MyProfile extends BaseController
{
    protected $employeesModel;
    protected $session;
    protected $validation;

    public function __construct()
    {
        // Load necessary helpers
        helper(['form', 'url', 'text', 'security','info']);
        
        // Initialize services
        $this->employeesModel = new employeesModel();
        $this->session = \Config\Services::session();
        $this->validation = \Config\Services::validation();
    }

    public function index()
    {
        if (!$this->session->has('emp_id')) {
            return redirect()->to(base_url('employee_portal/login'));
        }

        $emp_id = $this->session->get('emp_id');
        $data['employee'] = $this->employeesModel->find($emp_id);

        return view('employee_portal/my_profile', $data);
    }

    public function update()
    {
        if (!$this->session->has('emp_id')) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Not authenticated',
                'csrf_token' => csrf_hash()
            ]);
        }

        $emp_id = $this->session->get('emp_id');
        
        $rules = [
            'gender' => 'required|in_list[Male,Female]',
            'dobirth' => 'required|valid_date',
            'phone' => 'permit_empty|min_length[5]',
            'primary_email' => 'permit_empty|valid_email',
            'other_contacts' => 'permit_empty',
        ];

        // Only validate password if it's being updated
        if (!empty($this->request->getPost('password'))) {
            $rules['password'] = 'required|min_length[4]';
            $rules['confirm_password'] = 'required|matches[password]';
        }

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors(),
                'csrf_token' => csrf_hash()
            ]);
        }

        $updateData = [
            'gender' => $this->request->getPost('gender'),
            'dobirth' => $this->request->getPost('dobirth'),
            'phone' => $this->request->getPost('phone'),
            'primary_email' => $this->request->getPost('primary_email'),
            'other_contacts' => $this->request->getPost('other_contacts'),
        ];

        // Handle password update if provided
        if (!empty($this->request->getPost('password'))) {
            $updateData['password'] = $this->request->getPost('password');
        }

        try {
            $this->employeesModel->update($emp_id, $updateData);
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Profile updated successfully',
                'csrf_token' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to update profile',
                'csrf_token' => csrf_hash()
            ]);
        }
    }

    public function update_photo()
    {
        // Check for AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid request method',
                'csrf_token' => csrf_hash()
            ]);
        }

        // Check authentication
        if (!$this->session->has('emp_id')) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Not authenticated',
                'csrf_token' => csrf_hash()
            ]);
        }

        $emp_id = $this->session->get('emp_id');
        
        // Validate file upload
        $rules = [
            'id_photo' => [
                'rules' => 'uploaded[id_photo]|is_image[id_photo]|mime_in[id_photo,image/jpg,image/jpeg,image/png,image/gif]|max_size[id_photo,5120]',
                'errors' => [
                    'uploaded' => 'Please select a photo to upload',
                    'is_image' => 'The uploaded file must be an image',
                    'mime_in' => 'The file must be a JPG, PNG or GIF image',
                    'max_size' => 'The file size must not exceed 5MB'
                ]
            ]
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => $this->validator->getErrors()['id_photo'],
                'csrf_token' => csrf_hash()
            ]);
        }

        $photo = $this->request->getFile('id_photo');
        
        if (!$photo->isValid() || $photo->hasMoved()) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Invalid file upload',
                'csrf_token' => csrf_hash()
            ]);
        }

        try {
            // Get current employee data
            $employee = $this->employeesModel->find($emp_id);
            
            // Delete old photo if exists
            if (!empty($employee['id_photo'])) {
                $oldPhotoPath = ROOTPATH . $employee['id_photo'];
                if (file_exists($oldPhotoPath)) {
                    unlink($oldPhotoPath);
                }
            }

            // Generate new filename and move file
            $newName = $photo->getRandomName();
            $photo->move(ROOTPATH . 'public/uploads/id_photos', $newName);

            // Update database with new photo path
            $this->employeesModel->update($emp_id, [
                'id_photo' => 'public/uploads/id_photos/'.$newName
            ]);

            // Return success response with new photo URL
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Profile photo updated successfully',
                'data' => [
                    'photo_url' => base_url('public/uploads/id_photos/' . $newName)
                ],
                'csrf_token' => csrf_hash()
            ]);

        } catch (\Exception $e) {
            // If file was uploaded but database update failed, try to remove the uploaded file
            if (isset($newName)) {
                $uploadedPath = ROOTPATH . 'public/uploads/id_photos/' . $newName;
                if (file_exists($uploadedPath)) {
                    unlink($uploadedPath);
                }
            }

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to update profile photo',
                'csrf_token' => csrf_hash()
            ]);
        }
    }
} 