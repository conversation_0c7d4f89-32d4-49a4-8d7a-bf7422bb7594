<?php

namespace App\Controllers;

/**
 * UpgradeTest Controller
 * 
 * Used to test if the CodeIgniter 4.5 upgrade was successful
 */
class UpgradeTest extends AJAX_Controller
{
    /**
     * Display test page
     */
    public function getIndex()
    {
        // Get CodeIgniter version
        $version = \CodeIgniter\CodeIgniter::CI_VERSION;
        
        return $this->response->setContentType('text/html')->setBody('
            <!DOCTYPE html>
            <html>
            <head>
                <title>CodeIgniter Upgrade Test</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .card { border: 1px solid #ddd; border-radius: 5px; padding: 20px; margin-bottom: 20px; }
                    .success { color: green; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="card">
                        <h1>CodeIgniter Controller Test</h1>
                        <p>If you are seeing this page, your controller is working correctly!</p>
                        <p><strong>Current CodeIgniter Version:</strong> <span class="success">' . $version . '</span></p>
                        <p><strong>Environment:</strong> ' . ENVIRONMENT . '</p>
                        <p><strong>PHP Version:</strong> ' . PHP_VERSION . '</p>
                        <p><strong>Controller:</strong> UpgradeTest (extends AJAX_Controller)</p>
                        <hr>
                        <p>This controller is extending the new AJAX_Controller which disables the debug toolbar.</p>
                        <p><a href="' . base_url() . '">Return to homepage</a></p>
                        <p><a href="' . base_url('public/upgrade_check.php') . '">Run upgrade check</a></p>
                    </div>
                </div>
            </body>
            </html>
        ');
    }
    
    /**
     * Test JSON response
     */
    public function getJson()
    {
        $data = [
            'version' => \CodeIgniter\CodeIgniter::CI_VERSION,
            'php' => PHP_VERSION,
            'environment' => ENVIRONMENT,
            'timestamp' => time(),
        ];
        
        return $this->successResponse($data, 'Upgrade test successful');
    }
} 