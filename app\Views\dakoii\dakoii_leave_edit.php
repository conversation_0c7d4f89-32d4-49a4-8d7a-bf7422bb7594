<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-edit"></i> Edit Leave Type
                        </h5>
                        <small class="text-white-50">Update leave type information</small>
                    </div>
                    <a href="<?= base_url('dakoii-leave') ?>" class="btn btn-light">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
                <div class="card-body">
                    <?php if (session()->has('error')) : ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> <?= session('error') ?>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    <?php endif; ?>

                    <?= form_open('dakoii-leave/update/' . $leave_type['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="form-group">
                            <label for="name" class="font-weight-bold">
                                <i class="fas fa-tag"></i> Leave Type Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   placeholder="Enter leave type name (e.g., Annual Leave, Sick Leave)" 
                                   required 
                                   maxlength="100"
                                   value="<?= old('name', esc($leave_type['name'])) ?>">
                            <div class="invalid-feedback">
                                Please provide a valid leave type name.
                            </div>
                            <small class="form-text text-muted">
                                Maximum 100 characters. This name will be used throughout the system.
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="remarks" class="font-weight-bold">
                                <i class="fas fa-comment"></i> Remarks
                            </label>
                            <textarea class="form-control" 
                                      id="remarks" 
                                      name="remarks" 
                                      rows="4" 
                                      placeholder="Enter any additional notes or description for this leave type (optional)"><?= old('remarks', esc($leave_type['remarks'])) ?></textarea>
                            <small class="form-text text-muted">
                                Optional field for additional information about this leave type.
                            </small>
                        </div>

                        <!-- Display metadata -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold text-muted">Created Date:</label>
                                    <p class="form-control-plaintext">
                                        <?= date('F d, Y \a\t g:i A', strtotime($leave_type['created_at'])) ?>
                                    </p>
                                </div>
                            </div>
                            <?php if ($leave_type['updated_at']) : ?>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold text-muted">Last Updated:</label>
                                    <p class="form-control-plaintext">
                                        <?= date('F d, Y \a\t g:i A', strtotime($leave_type['updated_at'])) ?>
                                    </p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-group">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-save"></i> Update Leave Type
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <a href="<?= base_url('dakoii-leave') ?>" class="btn btn-secondary btn-block">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Character counter for name field
document.getElementById('name').addEventListener('input', function() {
    const maxLength = 100;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    // You can add a character counter here if needed
    if (remaining < 10) {
        this.style.borderColor = remaining < 0 ? '#dc3545' : '#ffc107';
    } else {
        this.style.borderColor = '';
    }
});
</script>

<?= $this->endSection() ?>
