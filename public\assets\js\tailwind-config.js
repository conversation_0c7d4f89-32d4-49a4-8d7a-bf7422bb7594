// Tailwind configuration for GovPSS
// This provides tailwind configuration that works with both local and CDN versions

// Create the tailwind object if it doesn't exist (for local CSS version)
if (typeof tailwind === 'undefined') {
  window.tailwind = {};
}

// Set the configuration
window.tailwind.config = {
  theme: {
    extend: {
      colors: {
        'png-red': '#CE1126',
        'png-black': '#000000',
        'png-gold': '#FCD116',
      },
      // Add typography plugin features
      typography: {
        DEFAULT: {
          css: {
            color: '#333',
            a: {
              color: '#CE1126',
              '&:hover': {
                color: '#a00d1d',
              },
            },
          },
        },
      },
      // Add aspect ratio utilities
      aspectRatio: {
        'auto': 'auto',
        'square': '1 / 1',
        '16/9': '16 / 9',
        '4/3': '4 / 3',
        '21/9': '21 / 9',
      },
    }
  },
  // No need to explicitly include line-clamp as it's included by default in Tailwind CSS v3.3+
  variants: {
    extend: {
      // Add variant extensions here if needed
      typography: ['responsive', 'dark'],
      aspectRatio: ['responsive'],
      lineClamp: ['responsive']
    }
  }
};

// Log a success message to console
console.log('Tailwind configuration loaded successfully'); 