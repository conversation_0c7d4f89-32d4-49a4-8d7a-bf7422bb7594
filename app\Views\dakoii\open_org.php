<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <!-- Back Button -->
    <div class="mb-3">
        <a href="<?= base_url('ddash') ?>" class="btn btn-outline-secondary">
            <i class="fa fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>

    <div class="row">
        <!-- Organization Details Card -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <?= esc($org['name']) ?>
                            <small class="text-white-50">(<?= esc($org['orgcode']) ?>)</small>
                        </h5>
                    </div>
                    <button type="button" class="btn btn-light" data-toggle="modal" data-target="#edit">
                        <i class="fas fa-edit"></i> Edit Organization
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Organization Logo -->
                        <div class="col-md-4 text-center mb-3">
                            <img class="img-thumbnail" src="<?= imgcheck($org['orglogo']) ?>" 
                                 alt="Organization Logo" style="max-width: 200px;">
                        </div>
                        <!-- Organization Info -->
                        <div class="col-md-8">
                            <h6 class="text-muted mb-3">Description</h6>
                            <p class="mb-4"><?= nl2br(esc($org['description'])) ?></p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Address Lock Country</h6>
                                            <p class="card-text">
                                                <?= isset($country_name) ? esc($country_name) : 'Not Set' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title text-muted">Address Lock Province</h6>
                                            <p class="card-text">
                                                <?= isset($province_name) ? esc($province_name) : 'Not Set' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge badge-<?= $org['is_active'] ? 'success' : 'danger' ?> mr-2">
                                <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                            </span>
                            <span class="badge badge-<?= $org['license_status'] == 'paid' ? 'success' : 'warning' ?>">
                                License: <?= ucfirst($org['license_status']) ?>
                            </span>
                        </div>
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#license_status">
                            <i class="fas fa-key"></i> Set License
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Administrators Card -->
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-shield"></i> System Administrators
                    </h5>
                    <button type="button" class="btn btn-light" data-toggle="modal" data-target="#sysadmin">
                        <i class="fas fa-plus"></i> New Admin
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Name</th>
                                    <th>Username</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($admins as $ur): ?>
                                <tr>
                                    <td><?= esc($ur['name']) ?></td>
                                    <td><?= esc($ur['username']) ?></td>
                                    <td>
                                        <span class="badge badge-info">
                                            <?= ucfirst(esc($ur['role'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= isset($ur['status']) && $ur['status'] == 1 ? 'success' : 'danger' ?>">
                                            <?= isset($ur['status']) && $ur['status'] == 1 ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary edit-admin" 
                                                data-id="<?= $ur['id'] ?>"
                                                data-name="<?= esc($ur['name']) ?>"
                                                data-username="<?= esc($ur['username']) ?>"
                                                data-role="<?= esc($ur['role']) ?>"
                                                data-active="<?= isset($ur['status']) ? $ur['status'] : '0' ?>"
                                                data-toggle="modal" 
                                                data-target="#editAdminModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Organization Modal -->
<div class="modal fade" id="edit" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Organization
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open_multipart('editorg') ?>
            <div class="modal-body">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Basic Information</h6>
                        
                        <div class="form-group">
                            <label>Organization Code</label>
                            <input type="text" class="form-control" name="orgcode" value="<?= esc($org['orgcode']) ?>" readonly>
                        </div>

                        <div class="form-group">
                            <label>Organization Name</label>
                            <input type="text" class="form-control" name="name" value="<?= esc($org['name']) ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Description</label>
                            <textarea class="form-control" name="description" rows="4"><?= esc($org['description']) ?></textarea>
                        </div>

                        <div class="form-group">
                            <label>Status</label>
                            <select name="status" class="form-control">
                                <option value="1" <?= $org['is_active'] == 1 ? 'selected' : '' ?>>Active</option>
                                <option value="0" <?= $org['is_active'] == 0 ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                    </div>

                    <!-- Location and Logo -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Location Settings</h6>

                        <div class="form-group">
                            <label>Address Lock Country</label>
                            <select name="country" class="form-control">
                                <option value="">Select Country</option>
                                <option value="<?= $set_country['id'] ?>" <?= $org['addlockcountry'] == $set_country['id'] ? 'selected' : '' ?>>
                                    <?= esc($set_country['name']) ?>
                                </option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Address Lock Province</label>
                            <select name="province" class="form-control">
                                <option value="">Select Province</option>
                                <?php foreach ($get_provinces as $prov): ?>
                                    <option value="<?= $prov['id'] ?>" 
                                            <?= $org['addlockprov'] == $prov['id'] ? 'selected' : '' ?>>
                                        <?= esc($prov['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <h6 class="text-muted mb-3 mt-4">Organization Logo</h6>

                        <div class="form-group">
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" name="org_logo" accept="image/*">
                                <label class="custom-file-label">Choose Logo</label>
                            </div>
                            <small class="form-text text-muted">Current logo will be replaced if new one is uploaded</small>
                        </div>

                        <?php if ($org['orglogo']): ?>
                        <div class="mt-2">
                            <img src="<?= imgcheck($org['orglogo']) ?>" alt="Current Logo" class="img-thumbnail" style="height: 100px;">
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <input type="hidden" name="id" value="<?= $org['id'] ?>">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Changes
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Add Edit Admin Modal -->
<div class="modal fade" id="editAdminModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit"></i> Edit System Admin
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('edit-admin') ?>
            <div class="modal-body">
                <input type="hidden" name="id" id="edit_admin_id">
                <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">

                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control" name="name" id="edit_admin_name" required>
                </div>

                <div class="form-group">
                    <label>Username</label>
                    <input type="text" class="form-control" name="username" id="edit_admin_username" required>
                </div>

                <div class="form-group">
                    <label>New Password</label>
                    <input type="password" class="form-control" name="password" placeholder="Leave blank to keep current password">
                    <small class="form-text text-muted">Only fill this if you want to change the password</small>
                </div>

                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" id="edit_admin_role" required>
                        <option value="user">User</option>
                        <option value="moderator">Moderator</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>

                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="edit_admin_active" name="is_active" value="1">
                    <label class="custom-control-label" for="edit_admin_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Admin
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- License Status Modal -->
<div class="modal fade" id="license_status" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-key"></i> Set License Status
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('dakoii_set_license_status') ?>
            <div class="modal-body">
                <div class="form-group">
                    <label>License Status</label>
                    <select class="form-control" name="license_status">
                        <option value="paid" <?= $org['license_status'] == 'paid' ? 'selected' : '' ?>>Paid</option>
                        <option value="trial" <?= $org['license_status'] == 'trial' ? 'selected' : '' ?>>Trial</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <input type="hidden" name="id" value="<?= $org['id'] ?>">
                <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Set Status
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Add System Admin Modal -->
<div class="modal fade" id="sysadmin" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i> Create System Admin
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('daddadmin') ?>
            <div class="modal-body">
                <div class="form-group">
                    <label>Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>

                <div class="form-group">
                    <label>Username</label>
                    <input type="text" class="form-control" name="username" required>
                </div>

                <div class="form-group">
                    <label>Password</label>
                    <input type="password" class="form-control" name="password" required>
                </div>

                <div class="form-group">
                    <label>Role</label>
                    <select class="form-control" name="role" required>
                        <option value="user">User</option>
                        <option value="moderator">Moderator</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>

                <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1">
                    <label class="custom-control-label" for="is_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Create Admin
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    margin-bottom: 1.5rem;
}
.card-header {
    border-bottom: none;
}
.badge {
    font-size: 85%;
}
.table td, .table th {
    vertical-align: middle;
}
.custom-switch {
    padding-left: 2.25rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit admin button clicks
    document.querySelectorAll('.edit-admin').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const name = this.dataset.name;
            const username = this.dataset.username;
            const role = this.dataset.role;
            const active = this.dataset.active;
            
            document.getElementById('edit_admin_id').value = id;
            document.getElementById('edit_admin_name').value = name;
            document.getElementById('edit_admin_username').value = username;
            document.getElementById('edit_admin_role').value = role;
            document.getElementById('edit_admin_active').checked = active === '1';
        });
    });
});
</script>

<?= $this->endSection() ?>