<?php

namespace App\Models;

use CodeIgniter\Model;

class orgModel extends Model
{
    protected $table      = 'dakoii_org';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'orgcode',
        'name',
        'description',
        'addlockprov',
        'addlockcountry',
        'orglogo',
        'is_locationlocked',
        'province_json',
        'district_json',
        'llg_json',
        'postal_address',
        'phones',
        'emails',
        'is_active',
        'license_status',
        'signature_filepath',
        'signature_position',
        'signature_name',
        'stamp_filepath',
        'approved_stamp_filepath',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules = [
        
    ];

    protected $validationMessages = [
        
    ];

    protected $skipValidation = false;

    // Database field types for reference
    protected $fieldTypes = [
        'id' => 'int',
        'orgcode' => 'varchar',
        'name' => 'varchar',
        'description' => 'text',
        'addlockprov' => 'varchar',
        'addlockcountry' => 'varchar',
        'orglogo' => 'varchar',
        'is_locationlocked' => 'tinyint',
        'province_json' => 'varchar',
        'district_json' => 'varchar',
        'llg_json' => 'varchar',
        'postal_address' => 'text',
        'phones' => 'text',
        'emails' => 'text',
        'is_active' => 'tinyint',
        'license_status' => 'varchar',
        'signature_filepath' => 'varchar',
        'signature_position' => 'varchar',
        'signature_name' => 'varchar',
        'stamp_filepath' => 'varchar',
        'approved_stamp_filepath' => 'varchar',
        'created_by' => 'int',
        'updated_by' => 'int',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp'
    ];
}
