<?= $this->extend('templates/nolstemp') ?>
<?= $this->section('content') ?>
<!-- Add Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<style>
    .select2-container--bootstrap-5 .select2-selection {
        min-height: 38px;
    }
    /* Custom styles for Select2 dropdown */
    .select2-container--bootstrap-5 .select2-dropdown {
        border-color: #dee2e6;
    }
    .select2-container--bootstrap-5 .select2-search__field {
        padding: 0.375rem 0.75rem;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
    .select2-container--bootstrap-5 .select2-search__field:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
</style>

<div class="container-fluid px-4">
    <h1 class="mt-4">Positions Management</h1>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3>Group: <?= esc($group['name']) ?></h3>
        <a href="<?= base_url('groups') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Groups
        </a>
    </div>

    <?php if (session()->has('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->has('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Positions
            <button type="button" class="btn btn-primary float-end" data-bs-toggle="modal" data-bs-target="#addModal">
                <i class="fas fa-plus"></i> Add Position
            </button>
        </div>
        <div class="card-body">
            <table id="positionsTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>Position Code</th>
                        <th>Designation</th>
                        <th>Appointment Type</th>
                        <th>Location</th>
                        <th>Employee</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($positions as $position): ?>
                        <tr>
                            <td><?= esc($position['position_code']) ?></td>
                            <td><?= esc($position['designation']) ?></td>
                            <td><?= esc($position['appointment_type']) ?></td>
                            <td><?= esc($position['location']) ?></td>
                            <td>
                                <?php
                                if ($position['employee_id']) {
                                    $employee = array_filter($employees, function ($e) use ($position) {
                                        return $e['emp_id'] == $position['employee_id'];
                                    });
                                    $employee = reset($employee);
                                    echo esc($employee ? $employee['fname'] . ' ' . $employee['lname'] : 'N/A');
                                } else {
                                    echo 'Vacant';
                                }
                                ?>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editModal<?= $position['id'] ?>">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <a href="<?= base_url('positions/delete/' . $position['id']) ?>"
                                    class="btn btn-sm btn-danger"
                                    onclick="return confirm('Are you sure you want to delete position <?= esc($position['position_code']) ?>?')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>

                        <!-- Edit Modal for <?= esc($position['designation']) ?> -->
                        <div class="modal fade" id="editModal<?= $position['id'] ?>" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Edit Position: <?= esc($position['designation']) ?></h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <form action="<?= base_url('positions/update/' . $position['id']) ?>" method="post">
                                        <?= csrf_field() ?>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label class="form-label">Position Code</label>
                                                <input type="text" class="form-control <?= session('validation.position_code') ? 'is-invalid' : '' ?>"
                                                    name="position_code" value="<?= esc($position['position_code']) ?>" required>
                                                <?php if (session('validation.position_code')): ?>
                                                    <div class="invalid-feedback">
                                                        <?= session('validation.position_code') ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Designation</label>
                                                <input type="text" class="form-control <?= session('validation.designation') ? 'is-invalid' : '' ?>"
                                                    name="designation" value="<?= esc($position['designation']) ?>" required>
                                                <?php if (session('validation.designation')): ?>
                                                    <div class="invalid-feedback">
                                                        <?= session('validation.designation') ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Appointment Type</label>
                                                <select class="form-select" name="appointment_type">
                                                    <option value="">Select Appointment Type</option>
                                                    <option value="Substantive" <?= $position['appointment_type'] == 'Substantive' ? 'selected' : '' ?>>Substantive</option>
                                                    <option value="Acting" <?= $position['appointment_type'] == 'Acting' ? 'selected' : '' ?>>Acting</option>
                                                    <option value="Caretaker" <?= $position['appointment_type'] == 'Caretaker' ? 'selected' : '' ?>>Caretaker</option>
                                                    <option value="Probationary" <?= $position['appointment_type'] == 'Probationary' ? 'selected' : '' ?>>Probationary</option>
                                                </select>
                                            </div>


                                            <div class="mb-3">
                                                <label class="form-label">Location</label>
                                                <input type="text" class="form-control" name="location" value="<?= esc($position['location']) ?>" required>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Employee</label>
                                                <select class="form-select employee-select" name="employee_id" style="width: 100%;">
                                                    <option value="">Select Employee (Vacant)</option>
                                                    <?php foreach ($employees as $employee): ?>
                                                        <option value="<?= $employee['emp_id'] ?>" <?= $position['employee_id'] == $employee['emp_id'] ? 'selected' : '' ?>>
                                                            <?= esc($employee['fname'] . ' ' . $employee['lname']) ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            <button type="submit" class="btn btn-primary">Update</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Modal -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Position</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('positions/store') ?>" method="post">
                <?= csrf_field() ?>
                <input type="hidden" name="group_id" value="<?= $group['id'] ?>">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Position Code</label>
                        <input type="text" class="form-control <?= session('validation.position_code') ? 'is-invalid' : '' ?>"
                            name="position_code" value="<?= old('position_code') ?>" required>
                        <?php if (session('validation.position_code')): ?>
                            <div class="invalid-feedback">
                                <?= session('validation.position_code') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Designation</label>
                        <input type="text" class="form-control <?= session('validation.designation') ? 'is-invalid' : '' ?>"
                            name="designation" value="<?= old('designation') ?>" required>
                        <?php if (session('validation.designation')): ?>
                            <div class="invalid-feedback">
                                <?= session('validation.designation') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Appointment Type</label>
                        <select name="appointment_type" class="form-select">
                            <option value="">Select Appointment Type</option>
                            <option value="Substantive">Substantive</option>
                            <option value="Acting">Acting</option>
                            <option value="Caretaker">Caretaker</option>
                            <option value="Probationary">Probationary</option>
                        </select>
                    </div>


                    <div class="mb-3">
                        <label class="form-label">Location</label>
                        <input type="text" class="form-control" name="location" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Employee</label>
                        <select class="form-select employee-select" name="employee_id" style="width: 100%;">
                            <option value="">Select Employee (Optional)</option>
                            <?php foreach ($employees as $employee): ?>
                                <option value="<?= $employee['emp_id'] ?>"><?= esc($employee['fname'] . ' ' . $employee['lname']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>


<!-- Add Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#positionsTable').DataTable();

        // Initialize Select2 for employee dropdowns
        function initSelect2(element) {
            $(element).select2({
                theme: 'bootstrap-5',
                width: '100%',
                allowClear: true,
                placeholder: 'Search and select employee...',
                dropdownParent: $(element).closest('.modal'),
                // Enable search box
                minimumResultsForSearch: 1,
                // Custom search matcher to search in both first and last name
                matcher: function(params, data) {
                    // If there are no search terms, return all of the data
                    if ($.trim(params.term) === '') {
                        return data;
                    }

                    // Do not display the item if there is no 'text' property
                    if (typeof data.text === 'undefined') {
                        return null;
                    }

                    // Search in both first name and last name (case insensitive)
                    if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                        return data;
                    }

                    // Return `null` if the term should not be displayed
                    return null;
                }
            });
        }

        // Initialize Select2 when modals are shown
        $('.modal').on('shown.bs.modal', function() {
            $(this).find('.employee-select').each(function() {
                initSelect2(this);
            });
        });

        // Destroy Select2 when modals are hidden to prevent duplicates
        $('.modal').on('hidden.bs.modal', function() {
            $(this).find('.employee-select').select2('destroy');
        });
    });
</script>


<?= $this->endSection() ?>