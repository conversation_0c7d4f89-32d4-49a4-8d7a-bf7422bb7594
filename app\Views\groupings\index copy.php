<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <?php if(session()->has('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if(session()->has('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Groups Management</h5>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                <i class="fas fa-plus"></i> Add New Group
            </button>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Parent Group</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($groups as $group): ?>
                    <tr>
                        <td><?= esc($group['name']) ?></td>
                        <td><?= esc($group['description']) ?></td>
                        <td><?= $group['parent_id'] ? esc($parents[$group['parent_id']] ?? 'None') : 'None' ?></td>
                        <td>
                            <div class="d-flex">
                                <a href="<?= base_url('positions/index/' . $group['id']) ?>" class="btn btn-sm btn-info me-2">
                                    <i class="fas fa-tasks"></i> Manage Positions
                                </a>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#editModal<?= $group['id'] ?>">
                                                <i class="fas fa-edit me-2"></i> Edit
                                            </button>
                                        </li>
                                        <li>
                                            <a href="<?= base_url('groups/delete/' . $group['id']) ?>" 
                                               class="dropdown-item text-danger"
                                               onclick="return confirm('Are you sure you want to delete <?= esc($group['name']) ?>?')">
                                                <i class="fas fa-trash me-2"></i> Delete
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <!-- Edit Modal for <?= esc($group['name']) ?> -->
                    <div class="modal fade" id="editModal<?= $group['id'] ?>" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Edit Group: <?= esc($group['name']) ?></h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <form action="<?= base_url('groups/update/' . $group['id']) ?>" method="post">
                                    <?= csrf_field() ?>
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label class="form-label">Name</label>
                                            <input type="text" class="form-control" name="name" value="<?= esc($group['name']) ?>" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" name="description" required><?= esc($group['description']) ?></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Parent Group</label>
                                            <select class="form-select" name="parent_id">
                                                <option value="">None</option>
                                                <?php foreach ($parents as $id => $name): ?>
                                                    <?php if ($id != $group['id']): // Prevent self-reference ?>
                                                    <option value="<?= $id ?>" <?= $group['parent_id'] == $id ? 'selected' : '' ?>><?= esc($name) ?></option>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        <button type="submit" class="btn btn-primary">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Organizational Chart Section -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">Organizational Structure</h5>
    </div>
    <div class="card-body">
        <div id="chart_wrapper">
            <div id="chart_div"></div>
        </div>
    </div>
</div>

<!-- Add Modal -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('groups/store') ?>" method="post">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Parent Group</label>
                        <select class="form-select" name="parent_id">
                            <option value="">None</option>
                            <?php foreach ($parents as $id => $name): ?>
                            <option value="<?= $id ?>"><?= esc($name) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>


<style>
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap');

#chart_wrapper {
    width: 100%;
    margin: auto;
    overflow: hidden;
}

#chart_div {
    width: 1000px; /* Base width */
}

.org-card {
    width: 180px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background: #ffffff;
    margin: 0 auto;
}

.org-card-header {
    background: linear-gradient(135deg, #6a11cb, #2575fc);
    color: #fff;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
}

.org-card-body {
    padding: 10px;
    text-align: center;
    color: #555;
    font-size: 14px;
}
</style>

<!-- Add jQuery first -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- Then Google Charts -->
<script src="https://www.gstatic.com/charts/loader.js"></script>
<script>
(function() {
    // Load the Google Charts library and initialize org chart with error handling
    try {
        google.charts.load('current', { 
            packages: ['orgchart'],
            callback: function() {
                // Initialize chart after library is loaded
                initChart();
            }
        });
    } catch (e) {
        console.error('Error loading Google Charts:', e);
        document.getElementById('chart_div').innerHTML = '<div class="alert alert-warning">Unable to load organizational chart. Please check your browser settings.</div>';
    }

    // Reset forms when modals are hidden
    $('.modal').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
    });

    function initChart() {
        try {
            var data = new google.visualization.DataTable();
            data.addColumn('string', 'Name');
            data.addColumn('string', 'Manager');
            data.addColumn('string', 'ToolTip');

            // Define organizational data
            var orgData = [
                { id: '1', name: 'CEO', parent: null },
                { id: '2', name: 'VP Sales', parent: '1' },
                { id: '3', name: 'VP Marketing', parent: '1' },
                { id: '4', name: 'Sales Manager 1', parent: '2' },
                { id: '5', name: 'Sales Manager 2', parent: '2' },
                { id: '6', name: 'Marketing Manager', parent: '3' }
            ];

            // Convert each object into a row with custom HTML
            orgData.forEach(function(item) {
                var parentId = item.parent ? item.parent : '';
                var nodeHTML = `
                    <div class="org-card">
                        <div class="org-card-header">${item.name}</div>
                        <div class="org-card-body">ID: ${item.id}</div>
                    </div>
                `;
                data.addRow([{ v: item.id, f: nodeHTML }, parentId, item.name]);
            });

            var chart = new google.visualization.OrgChart(document.getElementById('chart_div'));
            
            // Draw chart with options
            chart.draw(data, { 
                allowHtml: true,
                size: 'medium',
                nodeClass: 'org-node'
            });

            // Initial scale adjustment
            adjustChartScale();

            // Handle window resize with debouncing
            var resizeTimer;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(function() {
                    try {
                        chart.draw(data, { 
                            allowHtml: true,
                            size: 'medium',
                            nodeClass: 'org-node'
                        });
                        adjustChartScale();
                    } catch (e) {
                        console.error('Error redrawing chart:', e);
                    }
                }, 250);
            });

        } catch (e) {
            console.error('Error initializing chart:', e);
            document.getElementById('chart_div').innerHTML = '<div class="alert alert-warning">Unable to display organizational chart. Please try refreshing the page.</div>';
        }
    }

    function adjustChartScale() {
        try {
            var wrapper = document.getElementById('chart_wrapper');
            var chartDiv = document.getElementById('chart_div');
            if (!wrapper || !chartDiv) return;

            var containerWidth = wrapper.clientWidth;
            var scale = Math.min(containerWidth / BASE_CHART_WIDTH, 1);
            
            chartDiv.style.transformOrigin = 'top left';
            chartDiv.style.transform = 'scale(' + scale + ')';
            
            var unscaledHeight = chartDiv.offsetHeight;
            if (unscaledHeight) {
                wrapper.style.height = (unscaledHeight * scale) + 'px';
            }
        } catch (e) {
            console.error('Error adjusting chart scale:', e);
        }
    }

    // Constants
    var BASE_CHART_WIDTH = 1000;
})();
</script>
<?= $this->endSection() ?>
