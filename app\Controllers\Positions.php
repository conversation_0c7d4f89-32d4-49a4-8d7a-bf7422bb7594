<?php

namespace App\Controllers;

use App\Models\PositionsModel;
use App\Models\GroupingsModel;
use App\Models\employeesModel;

class Positions extends BaseController
{
    public $session;
    protected $positionsModel;
    protected $groupingsModel;
    protected $employeesModel;

    public function __construct()
    {
        // Load helpers
        helper(['form', 'url', 'session']);
        
        // Initialize session
        $this->session = session();
        
        // Initialize models
        $this->positionsModel = new PositionsModel();
        $this->groupingsModel = new GroupingsModel();
        $this->employeesModel = new employeesModel();
    }

    public function index($group_id = null)
    {
        if (!$group_id) {
            return redirect()->to('/groups')->with('error', 'No group specified');
        }

        $group = $this->groupingsModel->find($group_id);
        if (!$group) {
            return redirect()->to('/groups')->with('error', 'Group not found');
        }

        $positions = $this->positionsModel->getPositionsByGroup($group_id);
        if (!$positions['status']) {
            return redirect()->to('/groups')->with('error', $positions['message']);
        }

        $data = [
            'title' => 'Positions Management',
            'menu' => 'positions',
            'group' => $group,
            'positions' => $positions['data'],
            'employees' => $this->employeesModel->where('org_id', $this->session->get('org_id'))->where('status', 'active')->orderBy('fname', 'ASC')->findAll(),
            'validation' => \Config\Services::validation()
        ];

        return view('positions/index', $data);
    }

    public function store()
    {
        $data = [
            'org_id' => $this->session->get('org_id'),
            'group_id' => $this->request->getPost('group_id'),
            'position_code' => $this->request->getPost('position_code'),
            'designation' => $this->request->getPost('designation'),
            'appointment_type' => $this->request->getPost('appointment_type'),
            'location' => $this->request->getPost('location'),
            'employee_id' => $this->request->getPost('employee_id'),
            'created_by' => $this->session->get('user_id'),
            'updated_by' => $this->session->get('user_id')
        ];

        $result = $this->positionsModel->createPosition($data);
        
        if (!$result['status']) {
            return redirect()->back()
                ->with('error', $result['message'])
                ->with('validation', $result['errors'] ?? null)
                ->withInput();
        }

        return redirect()->back()->with('success', $result['message']);
    }

    public function update($id)
    {
        $data = [
            'position_code' => $this->request->getPost('position_code'),
            'designation' => $this->request->getPost('designation'),
            'appointment_type' => $this->request->getPost('appointment_type'),
            'location' => $this->request->getPost('location'),
            'employee_id' => $this->request->getPost('employee_id'),
            'updated_by' => $this->session->get('user_id')
        ];

        $result = $this->positionsModel->updatePosition($id, $data);
        
        if (!$result['status']) {
            return redirect()->back()
                ->with('error', $result['message'])
                ->with('validation', $result['errors'] ?? null)
                ->withInput();
        }

        return redirect()->back()->with('success', $result['message']);
    }

    public function delete($id)
    {
        $result = $this->positionsModel->deletePosition($id);
        
        if (!$result['status']) {
            return redirect()->back()->with('error', $result['message']);
        }

        return redirect()->back()->with('success', $result['message']);
    }

    public function appointments()
    {
        // Get positions with appointment data using the model method
        $positions = $this->positionsModel->getPositionsWithAppointments($this->session->get('org_id'));
        
        return view('positions/appointments', [
            'title' => 'Position Appointments',
            'menu' => 'appointments',
            'positions' => $positions
        ]);
    }

    public function appoint()
    {
        $position_id = $this->request->getPost('position_id');
        $employee_id = $this->request->getPost('employee_id');
        
        // Check if the employee is already appointed to another position
        $existingAppointment = $this->positionsModel->isEmployeeAppointed($employee_id, $position_id);
        
        if ($existingAppointment) {
            // Employee is already appointed to another position
            $errorMessage = sprintf(
                'This employee (File #%s) is already appointed to position %s - %s. An employee cannot be appointed to multiple positions.',
                esc($existingAppointment['fileno']),
                esc($existingAppointment['position_code']),
                esc($existingAppointment['designation'])
            );
            
            return redirect()->back()->with('error', $errorMessage);
        }
        
        // If we get here, the employee is not appointed elsewhere, proceed with appointment
        $result = $this->positionsModel->updatePosition($position_id, [
            'employee_id' => $employee_id,
            'appointment_type' => $this->request->getPost('appointment_type'),
            'appointment_date' => $this->request->getPost('appointment_date'),
            'appointment_by' => $this->session->get('user_id'),
            'updated_by' => $this->session->get('user_id')
        ]);
        
        if (!$result['status']) {
            return redirect()->back()->with('error', $result['message']);
        }

        return redirect()->to('appointments')->with('success', 'Appointment saved successfully');
    }

    /**
     * Display the appointment form for a specific position
     */
    public function position_appoint_employee($position_id = null)
    {
        if (!$position_id) {
            return redirect()->to('appointments')->with('error', 'No position specified');
        }
        
        // Get position with appointment data using the model method
        $position = $this->positionsModel->getPositionWithAppointment($position_id, $this->session->get('org_id'));
        
        if (!$position) {
            return redirect()->to('appointments')->with('error', 'Position not found');
        }
        
        // Get employees for dropdown using the employees model
        $employees = $this->employeesModel
            ->select('emp_id, fname, lname, fileno')
            ->where('org_id', $this->session->get('org_id'))
            ->where('status', 'active')
            ->orderBy('fname', 'ASC')
            ->findAll();
            
        return view('positions/position_appoint_employee', [
            'title' => 'Appoint Employee to Position',
            'menu' => 'appointments',
            'position' => $position,
            'employees' => $employees
        ]);
    }

    /**
     * Vacate a position by removing the employee assigned to it
     *
     * @param int $position_id The ID of the position to vacate
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function vacate($position_id)
    {
        // Load the positions model
        $positionsModel = new \App\Models\PositionsModel();
        
        // Get position details for the success message
        $position = $positionsModel->find($position_id);
        
        if (!$position) {
            return redirect()->to('appointments')
                ->with('error', 'Position not found');
        }
        
        // Check if the position already has no employee
        if (empty($position['employee_id'])) {
            return redirect()->to('positions/position_appoint_employee/' . $position_id)
                ->with('error', 'This position is already vacant');
        }
        
        // Attempt to vacate the position
        if ($positionsModel->vacatePosition($position_id)) {
            return redirect()->to('appointments')
                ->with('success', 'Position ' . esc($position['position_code']) . ' has been successfully vacated');
        } else {
            return redirect()->to('positions/position_appoint_employee/' . $position_id)
                ->with('error', 'Failed to vacate position. Please try again.');
        }
    }
} 