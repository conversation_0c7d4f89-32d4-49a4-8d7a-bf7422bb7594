<?php

namespace App\Controllers;

use App\Models\districtModel;
use App\Models\employeesModel;
use App\Models\orgModel;
use App\Models\provinceModel;
use App\Models\usersModel;
use App\Models\PositionsModel;
use App\Models\PayslipsModel;
use App\Models\LettersModel;

class AdminController extends BaseController
{
    public $session;
    public $usersModel;
    public $orgModel;
    public $provinceModel;
    public $districtModel;
    public $employeesModel;
    public $positionsModel;
    public $payslipsModel;
    public $lettersModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->employeesModel = new employeesModel();
        $this->positionsModel = new PositionsModel();
        $this->payslipsModel = new PayslipsModel();
        $this->lettersModel = new LettersModel();
    }

    public function admin_dashboard()
    {
        // Check if user is logged in and has admin role
        if (!$this->session->get('logged_in') || $this->session->get('role') !== 'admin') {
            return redirect()->to(base_url())->with('error', 'Access denied. Admin privileges required.');
        }

        // Get current organization ID from session
        $org_id = $this->session->get('org_id');

        $data = [
            'title' => 'Admin Dashboard',
            'menu' => 'dashboard',
            'total_users' => $this->usersModel->where('org_id', $org_id)->countAllResults(),
            'total_employees' => $this->employeesModel->where('org_id', $org_id)->countAllResults(),
            'total_positions' => $this->positionsModel->where('org_id', $org_id)->countAllResults(),
            'total_payslips' => $this->payslipsModel->where('org_id', $org_id)->countAllResults(),
            'total_organizations' => $this->orgModel->countAll(),
            'total_provinces' => $this->provinceModel->countAll(),
            'total_letters' => $this->lettersModel->where('org_id', $org_id)->countAllResults(),
            'recent_employees' => $this->employeesModel
                ->where('org_id', $org_id)
                ->orderBy('created_at', 'DESC')
                ->limit(5)
                ->find(),
            'recent_payslips' => $this->payslipsModel
                ->where('org_id', $org_id)
                ->orderBy('pay_date', 'DESC')
                ->limit(3)
                ->find(),
            'recent_letters' => $this->lettersModel
                ->where('org_id', $org_id)
                ->orderBy('created_at', 'DESC')
                ->limit(5)
                ->find(),
            'user_name' => $this->session->get('name'),
        ];

        return view('admin/admin_dashboard', $data);
    }
} 