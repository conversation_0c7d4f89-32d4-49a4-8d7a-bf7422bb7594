<?php

namespace App\Models;

use CodeIgniter\Model;

class LettersModel extends Model
{
    protected $table = 'letters';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useAutoIncrement = true;
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'unique_code', // unique code for each letter - use php uniqueid().time()
        'org_id',
        'letter_type',
        'letter_file_code', //HR-EMP-202502-0001
        'address_to',
        'subject',
        'content',
        'confirmation_status',
        'confirmation_remarks',
        'confirmed_by',
        'confirmed_at',
        'confirmed_by_email',
        'created_by',
        'updated_by'

    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation rules based on the table schema
    protected $validationRules = [
        'org_id' => 'required|integer',
        'letter_type' => 'required|max_length[50]',
        'address_to' => 'required',
        'subject' => 'required|max_length[255]',
        'content' => 'required',
        'confirmation_status' => 'required|in_list[approved,rejected,pending]',
        'confirmed_by' => 'permit_empty|integer',
        'confirmed_by_email' => 'permit_empty|valid_email|max_length[255]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [];

    // Before insert callback to ensure default values
    protected $beforeInsert = ['setDefaultConfirmationStatus'];

    protected function setDefaultConfirmationStatus(array $data)
    {
        if (!isset($data['data']['confirmation_status'])) {
            $data['data']['confirmation_status'] = 'pending';
        }
        return $data;
    }

    /**
     * Generates a unique letter file code
     * Format: HR-EMP-YYYYMM-XXXX where XXXX is an incremental number
     * @param string $prefix The prefix for the code (e.g., 'HR-EMP')
     * @return string The generated unique code
     */
    public function generateUniqueLetterFileCode($prefix = 'HR-EMP')
    {
        // Get the base code format
        $yearMonth = date('Ym');
        $baseCode = $prefix . '-' . $yearMonth . '-';
        
        // Find the highest number for this prefix and month
        $lastCode = $this->like('letter_file_code', $baseCode, 'after')
                        ->orderBy('letter_file_code', 'DESC')
                        ->first();
        
        if ($lastCode) {
            // Extract the number part and increment it
            $lastNumber = (int)substr($lastCode['letter_file_code'], -4);
            $newNumber = $lastNumber + 1;
        } else {
            // Start with 1 if no existing codes
            $newNumber = 1;
        }
        
        // Format the new code with padded zeros
        return $baseCode . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    // Get letters by organization
    public function getLettersByOrg($org_id)
    {
        return $this->where('org_id', $org_id)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    // Get letters by confirmation status
    public function getLettersByStatus($status, $org_id = null)
    {
        $query = $this->where('confirmation_status', $status);
        
        if ($org_id !== null) {
            $query->where('org_id', $org_id);
        }
        
        return $query->orderBy('created_at', 'DESC')
                    ->findAll();
    }
}
