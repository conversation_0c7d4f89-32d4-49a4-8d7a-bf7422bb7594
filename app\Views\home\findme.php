<?= $this->extend("templates/nolstemp"); ?>
<?= $this->section('content'); ?>

<!-- Loading Spinner -->
<div class="loader-wrapper loader-hidden">
    <div class="loader"></div>
</div>

<style>
    .loader-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.3s;
    }

    .loader {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #0d6efd;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .loader-hidden {
        opacity: 0;
        pointer-events: none;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<section class=" container">
    <div class="row p-2 d-flex justify-content-center">
        <div class=" col-md-5">

            <div class="card">
                <div class="card-header bg-primary text-light text-center">
                    <h4><?= $org['name'] ?></h4>
                </div>
                
                <div class="card-body text-center">

                    <h4><?= $emp['name'] ?></h4>
                    <p><?= $emp['fileno'] ?></p>

                </div>
                <div class="card-footer">
                <?= form_open('open_profile') ?>
                <div class="form-group">
                    <input type="password" name="password" placeholder="Password" class=" form-control form-control-lg">
                </div>
                <div class="form-group">
                    <input type="hidden" name="fileno" value="<?= $emp['fileno'] ?>">
                <button type="submit" class="btn btn-primary btn-block btn-lg"> <i class="fa fa-lock-open" aria-hidden="true"></i> OPEN PROFILE </button>
                </div>
                
                   
                    </form>
                </div>
                
            </div>
        </div>
    </div>
</section>


<script>
    // Show loading spinner during form submission
    document.querySelector('form').addEventListener('submit', function() {
        document.querySelector('.loader-wrapper').classList.remove('loader-hidden');
    });

    // Hide loading spinner when page is fully loaded
    window.addEventListener('load', function() {
        document.querySelector('.loader-wrapper').classList.add('loader-hidden');
    });
</script>

</body>

<?= $this->endSection() ?>