<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<?php
// Calculate summary statistics
$totalPositions = count($positions);
$vacantPositions = 0;
$appointmentTypesNotSet = 0;
$appointmentDatesNotSet = 0;
$appointmentTypes = [];

foreach ($positions as $position) {
    // Count vacant positions
    if (empty($position['employee_id'])) {
        $vacantPositions++;
    }
    
    // Count appointment types
    $type = $position['appointment_type'] ?? 'Not Set';
    if (!isset($appointmentTypes[$type])) {
        $appointmentTypes[$type] = 0;
    }
    $appointmentTypes[$type]++;
    
    // Count appointments not set
    if (empty($position['appointment_type'])) {
        $appointmentTypesNotSet++;
    }
    
    // Count appointment dates not set (empty or '0000-00-00')
    if (empty($position['appointment_date']) || $position['appointment_date'] == '0000-00-00') {
        $appointmentDatesNotSet++;
    }
}

// Sort appointment types by count (descending)
arsort($appointmentTypes);
?>

<!-- Summary Cards Section -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <h6 class="card-title fw-bold text-muted mb-3">Position Summary</h6>
                <div class="d-flex align-items-center mb-2">
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                        <i class="fas fa-briefcase text-white"></i>
                    </div>
                    <div>
                        <h3 class="mb-0 fw-bold"><?= $totalPositions ?></h3>
                        <p class="text-muted mb-0">Total Positions</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <h6 class="card-title fw-bold text-muted mb-3">Vacancy Status</h6>
                <div class="d-flex align-items-center mb-2">
                    <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                        <i class="fas fa-user-slash text-white"></i>
                    </div>
                    <div>
                        <h3 class="mb-0 fw-bold"><?= $vacantPositions ?></h3>
                        <p class="text-muted mb-0">Vacant Positions</p>
                    </div>
                </div>
                <div class="progress mt-2" style="height: 8px;">
                    <div class="progress-bar bg-warning" role="progressbar" style="width: <?= ($totalPositions > 0) ? ($vacantPositions / $totalPositions * 100) : 0 ?>%"></div>
                </div>
                <small class="text-muted"><?= ($totalPositions > 0) ? round($vacantPositions / $totalPositions * 100, 1) : 0 ?>% vacancy rate</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <h6 class="card-title fw-bold text-muted mb-3">Appointment Types</h6>
                <?php foreach (array_slice($appointmentTypes, 0, 3) as $type => $count): ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span><?= esc($type) ?></span>
                    <span class="badge bg-primary rounded-pill"><?= $count ?></span>
                </div>
                <?php endforeach; ?>
                
                <?php if (count($appointmentTypes) > 3): ?>
                <small class="text-muted">And <?= count($appointmentTypes) - 3 ?> more types</small>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <h6 class="card-title fw-bold text-muted mb-3">Appointment Dates Not Set</h6>
                <div class="d-flex align-items-center mb-2">
                    <div class="bg-danger rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                        <i class="fas fa-calendar-times text-white"></i>
                    </div>
                    <div>
                        <h3 class="mb-0 fw-bold"><?= $appointmentDatesNotSet ?></h3>
                        <p class="text-muted mb-0">Missing Dates</p>
                    </div>
                </div>
                <div class="progress mt-2" style="height: 8px;">
                    <div class="progress-bar bg-danger" role="progressbar" style="width: <?= ($totalPositions > 0) ? ($appointmentDatesNotSet / $totalPositions * 100) : 0 ?>%"></div>
                </div>
                <small class="text-muted"><?= ($totalPositions > 0) ? round($appointmentDatesNotSet / $totalPositions * 100, 1) : 0 ?>% of positions</small>
            </div>
        </div>
    </div>
</div>

<!-- Main Table Card -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 fw-bold">Position Appointments</h5>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (session()->has('error')): ?>
                    <div class="alert alert-danger">
                        <?= session('error') ?>
                    </div>
                <?php endif; ?>
                
                <?php if (session()->has('success')): ?>
                    <div class="alert alert-success">
                        <?= session('success') ?>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-hover" id="appointmentsTable">
                        <thead>
                            <tr>
                                <th>Position Code</th>
                                <th>Designation</th>
                                <th>Group</th>
                                <th>Current Appointee</th>
                                <th>Appointment Type</th>
                                <th>Appointment Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positions as $position): ?>
                            <tr>
                                <td><?= esc($position['position_code']) ?></td>
                                <td><?= esc($position['designation']) ?></td>
                                <td><?= esc($position['group_name'] ?? 'Not Assigned') ?></td>
                                <td>
                                    <?php 
                                    // Directly use the joined data
                                    echo ($position['fname'] && $position['lname']) 
                                        ? esc($position['fname'] . ' ' . $position['lname']) 
                                        : '<span class="text-muted">Vacant</span>';
                                    ?>
                                </td>
                                <td><?= esc($position['appointment_type'] ?? 'Not Set') ?></td>
                                <td><?= ($position['appointment_date'] && $position['appointment_date'] != '0000-00-00') ? date('M d, Y', strtotime($position['appointment_date'])) : 'Not Set' ?></td>
                                <td>
                                    <a href="<?= base_url('positions/position_appoint_employee/' . $position['id']) ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-user-plus me-1"></i> Appoint
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTables initialization script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTable with simple configuration
        $('#appointmentsTable').DataTable({
            pageLength: 10,
            order: [[0, 'asc']]
        });
    });
</script>
<?= $this->endSection() ?>