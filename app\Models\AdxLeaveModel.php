<?php

namespace App\Models;

use CodeIgniter\Model;

class AdxLeaveModel extends Model
{
    protected $table = 'adx_leave';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'name',
        'remarks',
        'created_by',
        'updated_by',
        'is_deleted',
        'deleted_at',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation rules based on the table schema
    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'remarks' => 'permit_empty',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Name is required',
            'max_length' => 'Name cannot exceed 100 characters'
        ]
    ];

    protected $skipValidation = false;

    // Before insert callback to ensure default values
    protected $beforeInsert = ['setDefaultValues'];
    protected $beforeUpdate = ['setUpdatedValues'];

    protected function setDefaultValues(array $data)
    {
        if (!isset($data['data']['is_deleted'])) {
            $data['data']['is_deleted'] = false;
        }
        return $data;
    }

    protected function setUpdatedValues(array $data)
    {
        // Any additional logic for updates can be added here
        return $data;
    }

    // Database field types for reference
    protected $fieldTypes = [
        'id' => 'int',
        'name' => 'varchar',
        'remarks' => 'text',
        'created_by' => 'int',
        'updated_by' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'is_deleted' => 'boolean',
        'deleted_at' => 'datetime',
        'deleted_by' => 'int'
    ];

    /**
     * Get all active leave types (not deleted)
     *
     * @return array Array of active leave types
     */
    public function getActiveLeaveTypes()
    {
        return $this->where('is_deleted', false)
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get leave type by ID
     *
     * @param int $id Leave type ID
     * @param bool $includeDeleted Whether to include deleted records
     * @return array|null Leave type data or null if not found
     */
    public function getLeaveTypeById($id, $includeDeleted = false)
    {
        $query = $this->where('id', $id);

        if (!$includeDeleted) {
            $query->where('is_deleted', false);
        }

        return $query->first();
    }

    /**
     * Get leave type by name
     *
     * @param string $name Leave type name
     * @param bool $includeDeleted Whether to include deleted records
     * @return array|null Leave type data or null if not found
     */
    public function getLeaveTypeByName($name, $includeDeleted = false)
    {
        $query = $this->where('name', $name);

        if (!$includeDeleted) {
            $query->where('is_deleted', false);
        }

        return $query->first();
    }

    /**
     * Search leave types by name
     *
     * @param string $searchTerm Search term
     * @param bool $includeDeleted Whether to include deleted records
     * @return array Array of matching leave types
     */
    public function searchLeaveTypes($searchTerm, $includeDeleted = false)
    {
        $query = $this->like('name', $searchTerm);

        if (!$includeDeleted) {
            $query->where('is_deleted', false);
        }

        return $query->orderBy('name', 'ASC')->findAll();
    }

    /**
     * Soft delete a leave type record
     *
     * @param int $id Leave type ID
     * @param int $deleted_by User ID who is deleting the record
     * @return bool Success status
     */
    public function softDelete($id, $deleted_by = null)
    {
        $data = [
            'is_deleted' => true,
            'deleted_at' => date('Y-m-d H:i:s'),
            'deleted_by' => $deleted_by
        ];

        return $this->update($id, $data);
    }

    /**
     * Restore a soft deleted leave type record
     *
     * @param int $id Leave type ID
     * @return bool Success status
     */
    public function restore($id)
    {
        $data = [
            'is_deleted' => false,
            'deleted_at' => null,
            'deleted_by' => null
        ];

        return $this->update($id, $data);
    }

    /**
     * Create a new leave type
     *
     * @param array $data Leave type data
     * @return int|bool Insert ID on success, false on failure
     */
    public function createLeaveType($data)
    {
        // Validate required fields
        if (empty($data['name'])) {
            return false;
        }

        // Check if leave type with same name already exists
        $existing = $this->getLeaveTypeByName($data['name']);
        if ($existing) {
            return false; // Duplicate name
        }

        // Set default values
        $data['is_deleted'] = false;

        return $this->insert($data);
    }

    /**
     * Update an existing leave type
     *
     * @param int $id Leave type ID
     * @param array $data Updated leave type data
     * @return bool Success status
     */
    public function updateLeaveType($id, $data)
    {
        // Check if record exists and is not deleted
        $existing = $this->getLeaveTypeById($id);
        if (!$existing) {
            return false;
        }

        // If name is being updated, check for duplicates
        if (isset($data['name']) && $data['name'] !== $existing['name']) {
            $duplicate = $this->getLeaveTypeByName($data['name']);
            if ($duplicate && $duplicate['id'] !== $id) {
                return false; // Duplicate name
            }
        }

        return $this->update($id, $data);
    }

    /**
     * Get leave types with pagination
     *
     * @param int $perPage Number of records per page
     * @param int $page Current page number
     * @param bool $includeDeleted Whether to include deleted records
     * @return array Paginated leave types
     */
    public function getPaginatedLeaveTypes($perPage = 10, $page = 1, $includeDeleted = false)
    {
        $query = $this;

        if (!$includeDeleted) {
            $query = $query->where('is_deleted', false);
        }

        return $query->orderBy('name', 'ASC')
                    ->paginate($perPage, 'default', $page);
    }

    /**
     * Get total count of leave types
     *
     * @param bool $includeDeleted Whether to include deleted records
     * @return int Total count
     */
    public function getTotalCount($includeDeleted = false)
    {
        $query = $this;

        if (!$includeDeleted) {
            $query = $query->where('is_deleted', false);
        }

        return $query->countAllResults();
    }
} 