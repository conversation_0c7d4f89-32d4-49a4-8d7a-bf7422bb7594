<?php

namespace App\Models;

use CodeIgniter\Model;

class AdxLeaveModel extends Model
{
    protected $table = 'adx_leave';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'code',
        'name',
        'remarks',
        'created_by',
        'updated_by',
        'is_deleted',
        'deleted_at',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation rules based on the table schema
    protected $validationRules = [
        'code' => 'required|max_length[50]|is_unique[adx_leave.code,id,{id}]',
        'name' => 'required|max_length[100]',
        'remarks' => 'permit_empty',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'code' => [
            'required' => 'Code is required',
            'max_length' => 'Code cannot exceed 50 characters',
            'is_unique' => 'This code already exists'
        ],
        'name' => [
            'required' => 'Name is required',
            'max_length' => 'Name cannot exceed 100 characters'
        ]
    ];

    protected $skipValidation = false;

    // Before insert callback to ensure default values
    protected $beforeInsert = ['setDefaultValues'];
    protected $beforeUpdate = ['setUpdatedValues'];

    protected function setDefaultValues(array $data)
    {
        if (!isset($data['data']['is_deleted'])) {
            $data['data']['is_deleted'] = false;
        }
        return $data;
    }

    protected function setUpdatedValues(array $data)
    {
        // Any additional logic for updates can be added here
        return $data;
    }

    // Database field types for reference
    protected $fieldTypes = [
        'id' => 'int',
        'code' => 'varchar',
        'name' => 'varchar',
        'remarks' => 'text',
        'created_by' => 'int',
        'updated_by' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'is_deleted' => 'boolean',
        'deleted_at' => 'datetime',
        'deleted_by' => 'int'
    ];

    /**
     * Get all active leave types (not deleted)
     *
     * @return array Array of active leave types
     */
    public function getActiveLeaveTypes()
    {
        return $this->where('is_deleted', false)
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get leave type by ID
     *
     * @param int $id Leave type ID
     * @param bool $includeDeleted Whether to include deleted records
     * @return array|null Leave type data or null if not found
     */
    public function getLeaveTypeById($id, $includeDeleted = false)
    {
        $query = $this->where('id', $id);

        if (!$includeDeleted) {
            $query->where('is_deleted', false);
        }

        return $query->first();
    }

    /**
     * Get leave type by name
     *
     * @param string $name Leave type name
     * @param bool $includeDeleted Whether to include deleted records
     * @return array|null Leave type data or null if not found
     */
    public function getLeaveTypeByName($name, $includeDeleted = false)
    {
        $query = $this->where('name', $name);

        if (!$includeDeleted) {
            $query->where('is_deleted', false);
        }

        return $query->first();
    }

    /**
     * Get leave type by code
     *
     * @param string $code Leave type code
     * @param bool $includeDeleted Whether to include deleted records
     * @return array|null Leave type data or null if not found
     */
    public function getLeaveTypeByCode($code, $includeDeleted = false)
    {
        $query = $this->where('code', $code);

        if (!$includeDeleted) {
            $query->where('is_deleted', false);
        }

        return $query->first();
    }

    /**
     * Search leave types by name
     *
     * @param string $searchTerm Search term
     * @param bool $includeDeleted Whether to include deleted records
     * @return array Array of matching leave types
     */
    public function searchLeaveTypes($searchTerm, $includeDeleted = false)
    {
        $query = $this->like('name', $searchTerm);

        if (!$includeDeleted) {
            $query->where('is_deleted', false);
        }

        return $query->orderBy('name', 'ASC')->findAll();
    }

    /**
     * Soft delete a leave type record
     *
     * @param int $id Leave type ID
     * @param int $deleted_by User ID who is deleting the record
     * @return bool Success status
     */
    public function softDelete($id, $deleted_by = null)
    {
        $data = [
            'is_deleted' => true,
            'deleted_at' => date('Y-m-d H:i:s'),
            'deleted_by' => $deleted_by
        ];

        return $this->update($id, $data);
    }

    /**
     * Restore a soft deleted leave type record
     *
     * @param int $id Leave type ID
     * @return bool Success status
     */
    public function restore($id)
    {
        $data = [
            'is_deleted' => false,
            'deleted_at' => null,
            'deleted_by' => null
        ];

        return $this->update($id, $data);
    }

    /**
     * Create a new leave type
     *
     * @param array $data Leave type data
     * @return int|bool Insert ID on success, false on failure
     */
    public function createLeaveType($data)
    {
        // Validate required fields
        if (empty($data['name']) || empty($data['code'])) {
            return false;
        }

        // Check if leave type with same name already exists
        $existingName = $this->getLeaveTypeByName($data['name']);
        if ($existingName) {
            return false; // Duplicate name
        }

        // Check if leave type with same code already exists
        $existingCode = $this->getLeaveTypeByCode($data['code']);
        if ($existingCode) {
            return false; // Duplicate code
        }

        // Set default values
        $data['is_deleted'] = false;

        return $this->insert($data);
    }

    /**
     * Update an existing leave type
     *
     * @param int $id Leave type ID
     * @param array $data Updated leave type data
     * @return array Result with status and message
     */
    public function updateLeaveType($id, $data)
    {
        // Check if record exists and is not deleted
        $existing = $this->getLeaveTypeById($id);
        if (!$existing) {
            return ['status' => false, 'message' => 'Leave type not found'];
        }

        // If name is being updated, check for duplicates
        if (isset($data['name']) && $data['name'] !== $existing['name']) {
            $duplicate = $this->getLeaveTypeByName($data['name']);
            if ($duplicate && $duplicate['id'] !== $id) {
                return ['status' => false, 'message' => 'Leave type name already exists'];
            }
        }

        // If code is being updated, check for duplicates
        if (isset($data['code']) && $data['code'] !== $existing['code']) {
            $duplicate = $this->getLeaveTypeByCode($data['code']);
            if ($duplicate && $duplicate['id'] !== $id) {
                return ['status' => false, 'message' => 'Leave type code already exists'];
            }
        }

        $result = $this->update($id, $data);

        if ($result) {
            return ['status' => true, 'message' => 'Leave type updated successfully'];
        } else {
            return ['status' => false, 'message' => 'Failed to update leave type'];
        }
    }

    /**
     * Get leave types with pagination
     *
     * @param int $perPage Number of records per page
     * @param int $page Current page number
     * @param bool $includeDeleted Whether to include deleted records
     * @return array Paginated leave types
     */
    public function getPaginatedLeaveTypes($perPage = 10, $page = 1, $includeDeleted = false)
    {
        $query = $this;

        if (!$includeDeleted) {
            $query = $query->where('is_deleted', false);
        }

        return $query->orderBy('name', 'ASC')
                    ->paginate($perPage, 'default', $page);
    }

    /**
     * Get total count of leave types
     *
     * @param bool $includeDeleted Whether to include deleted records
     * @return int Total count
     */
    public function getTotalCount($includeDeleted = false)
    {
        $query = $this;

        if (!$includeDeleted) {
            $query = $query->where('is_deleted', false);
        }

        return $query->countAllResults();
    }
}