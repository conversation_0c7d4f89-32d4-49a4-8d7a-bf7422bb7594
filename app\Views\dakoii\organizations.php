<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-building"></i> Organizations</h5>
            <button type="button" class="btn btn-light" data-toggle="modal" data-target="#addOrgModal">
                <i class="fas fa-plus"></i> Add Organization
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th>#</th>
                            <th>Logo</th>
                            <th>Name</th>
                            <th>Code</th>
                            <th>Description</th>
                            <th>Location Lock</th>
                            <th>License Status</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $i = 1; foreach ($organizations as $org): ?>
                        <tr>
                            <td><?= $i++ ?></td>
                            <td>
                                <?php if (!empty($org['orglogo'])): ?>
                                    <img src="<?= imgcheck($org['orglogo']) ?>" alt="Logo" style="height: 30px;">
                                <?php else: ?>
                                    <i class="fas fa-building text-muted"></i>
                                <?php endif; ?>
                            </td>
                            <td><?= esc($org['name']) ?></td>
                            <td><?= esc($org['orgcode']) ?></td>
                            <td><?= esc($org['description']) ?></td>
                            <td>
                                <?php if ($org['is_locationlocked']): ?>
                                    <span class="badge badge-warning">
                                        <?= $org['addlockcountry'] ?> - <?= $org['addlockprov'] ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-success">Unlocked</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge badge-<?= $org['license_status'] ? 'success' : 'danger' ?>">
                                    <?= $org['license_status'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge badge-<?= $org['is_active'] ? 'success' : 'danger' ?>">
                                    <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                                </span>
                            </td>
                            <td>
                                <a href="<?= base_url('dopen_org/' . $org['orgcode']) ?>" 
                                   class="btn btn-sm btn-info" 
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Organization Modal -->
<div class="modal fade" id="addOrgModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Add New Organization</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open_multipart('daddorg') ?>
            <div class="modal-body">
                <div class="form-group">
                    <label>Organization Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>
                
                <div class="form-group">
                    <label>Description</label>
                    <textarea class="form-control" name="description" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label>Organization Logo</label>
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" name="org_logo" accept="image/*">
                        <label class="custom-file-label">Choose Logo</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Save Organization</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?> 