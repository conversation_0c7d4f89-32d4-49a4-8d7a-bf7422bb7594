<?= $this->extend('templates/employees_portal_temp') ?>

<?= $this->section('content') ?>
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">My Payslips</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="<?= base_url('employee_portal/dashboard') ?>">Dashboard</a></li>
                                <li class="breadcrumb-item active">Payslips</li>
                            </ol>
                        </nav>
                    </div>

                    <?php if(session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger" role="alert">
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <?php if(session()->getFlashdata('success')): ?>
                        <div class="alert alert-success" role="alert">
                            <?= session()->getFlashdata('success') ?>
                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-hover" id="payslipsTable">
                            <thead>
                                <tr>
                                    <th>Pay Date</th>
                                    <th>Pay Number</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($payslips) && !empty($payslips)): ?>
                                    <?php foreach ($payslips as $payslip): ?>
                                        <tr>
                                            <td><?= date('d M Y', strtotime($payslip['pay_date'])) ?></td>
                                            <td><?= esc($payslip['pay_no']) ?></td>
                                            <td>
                                                <a href="<?= base_url('employee_portal/my_payslips/download/' . $payslip['id']) ?>" 
                                                   class="btn btn-sm btn-primary">
                                                    <i class="fas fa-download me-1"></i> Download
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No payslips found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- New Card for Payslip Search -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-4">Search Historical Payslips </h5>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <select class="form-select" id="payslipSelect" style="width: 100%;">
                                <option value="">Select a payslip...</option>
                                <?php foreach($all_payslips as $pay): ?>
                                    <option value="<?= $pay['id'] ?>">Pay No: <?= $pay['pay_no'] ?> - Date: <?= date('d M Y', strtotime($pay['pay_date'])) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div id="downloadContainer" style="display: none;">
                                <a href="#" id="downloadLink" class="btn btn-primary w-100">
                                    <i class="fas fa-download me-1"></i> Download Payslip
                                </a>
                            </div>
                            <div id="errorContainer" style="display: none;">
                                <div class="alert alert-danger mb-0" role="alert">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    <span id="errorMessage"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<script>
$(document).ready(function() {
    

    // Initialize Select2
    $('#payslipSelect').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select a payslip...',
        allowClear: true,
        width: '100%'
    });

    // Handle payslip selection
    $('#payslipSelect').on('select2:select', function(e) {
        const payslipId = e.target.value;
        checkPayslip(payslipId);
    });

    // Handle payslip clear
    $('#payslipSelect').on('select2:clear', function() {
        $('#downloadContainer').hide();
        $('#errorContainer').hide();
    });

    // Function to check payslip
    function checkPayslip(payslipId) {
        $.ajax({
            url: '<?= base_url('employee_portal/my_payslips/checkPayslip') ?>/' + payslipId,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    $('#errorContainer').hide();
                    $('#downloadContainer').show();
                    $('#downloadLink').attr('href', '<?= base_url('employee_portal/my_payslips/download') ?>/' + payslipId);
                } else {
                    $('#downloadContainer').hide();
                    $('#errorContainer').show();
                    $('#errorMessage').text(response.message);
                }
            },
            error: function() {
                $('#downloadContainer').hide();
                $('#errorContainer').show();
                $('#errorMessage').text('Error processing request');
            }
        });
    }
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 