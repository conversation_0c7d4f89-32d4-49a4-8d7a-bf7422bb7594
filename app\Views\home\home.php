<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GovPSS - Government Public Servants Support System</title>
  <link rel="icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">
  <meta name="theme-color" content="#000000">
  <meta name="description" content="Government Public Servants Support System">
  <link rel="manifest" href="<?= base_url() ?>public/manifest.json">
  <link rel="apple-touch-icon" href="<?= base_url() ?>public/assets/system_img/pwa-192x192.png">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="GovPSS">
  <!-- Using Tailwind from CDN as preferred -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Optimized Google Fonts loading -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
  <noscript>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  </noscript>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- Use external tailwind config file -->
  <script src="<?= base_url() ?>public/assets/js/tailwind-config.js"></script>
  <style>
    body {
      font-family: 'Inter', 'Inter Fallback', sans-serif;
      scroll-behavior: smooth;
    }

    html {
      scroll-behavior: smooth;
    }

    .gradient-bg {
      background: linear-gradient(135deg, rgba(206, 17, 38, 0.9), rgba(0, 0, 0, 0.9));
    }

    .hover-card {
      transition: transform 0.3s ease;
    }

    .hover-card:hover {
      transform: translateY(-5px);
    }

    /* Loading Spinner Styles */
    .loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.3s;
    }

    .loader {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #CE1126;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loader-hidden {
      opacity: 0;
      pointer-events: none;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .page-transition {
      transition: opacity 0.3s;
    }

    /* Font fallback system if Google Fonts are blocked */
    @font-face {
      font-family: 'Inter Fallback';
      font-style: normal;
      font-weight: 400;
      font-display: swap;
      src: local('Arial');
    }
    @font-face {
      font-family: 'Inter Fallback';
      font-style: normal;
      font-weight: 500;
      font-display: swap;
      src: local('Arial Bold');
    }
    @font-face {
      font-family: 'Inter Fallback';
      font-style: normal;
      font-weight: 600;
      font-display: swap;
      src: local('Arial Bold');
    }
    @font-face {
      font-family: 'Inter Fallback';
      font-style: normal;
      font-weight: 700;
      font-display: swap;
      src: local('Arial Bold');
    }
  </style>
</head>

<body class="bg-gray-50">
  <!-- Loading Spinner -->
  <div class="loader-wrapper">
    <div class="loader"></div>
  </div>
  <!-- Navigation Menu -->
  <nav class="bg-png-black text-white shadow-lg fixed w-full z-50">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="text-2xl font-bold">
        <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="System Logo" class="w-auto h-12">
      </div>
      <div class="flex items-center space-x-6">
        <a href="#home" class="hover:text-png-gold">Home</a>
        <a href="#features" class="hover:text-png-gold">Features</a>
        <a href="#how-it-works" class="hover:text-png-gold">How It Works</a>
        <a href="#login" class="hover:text-png-gold">Admin Login</a>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="gradient-bg text-white pt-32 pb-20">
    <div class="container mx-auto px-6 text-center">
      <h1 class="text-5xl font-bold mb-6 text-png-gold">Welcome to GovPSS</h1>
      <p class="text-xl mb-8 text-white/90">Your one-stop platform for payslips, confirmation letters, and salary analysis.</p>
      <div class="max-w-2xl mx-auto">
        <form method="POST" action="<?= base_url('employee_portal/search') ?>" class="flex items-center bg-white rounded-lg overflow-hidden shadow-lg border-2 border-png-gold">
          <?= csrf_field() ?>
          <input
            type="text"
            name="fileno"
            placeholder="Enter file number"
            required
            class="w-full px-6 py-4 text-gray-700 focus:outline-none" />
          <button type="submit" class="bg-png-red text-white px-8 py-4 hover:bg-red-700 transition duration-300 flex items-center">
            <span>Find Me</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </form>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="py-20 bg-gray-50">
    <div class="container mx-auto px-6">
      <h2 class="text-4xl font-bold text-center mb-4 text-png-black">System Features</h2>
      <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">Discover the powerful tools and features designed to make your public service experience seamless.</p>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Feature Card -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center hover-card hover:border-png-gold hover:border-2 transition duration-300">
          <div class="text-png-red text-4xl mb-4">📄</div>
          <h3 class="text-xl font-bold mb-4">Payslip Distribution</h3>
          <p class="text-gray-600">Access and download your payslips securely anytime, anywhere.</p>
        </div>
        <!-- Feature Card -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
          <div class="text-png-red text-4xl mb-4">✉️</div>
          <h3 class="text-xl font-bold mb-4">Confirmation Letters</h3>
          <p class="text-gray-600">Request and receive confirmation letters with ease.</p>
        </div>
        <!-- Feature Card -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
          <div class="text-png-red text-4xl mb-4">📊</div>
          <h3 class="text-xl font-bold mb-4">Salary Analysis</h3>
          <p class="text-gray-600">Analyze your salary trends based on your payslips.</p>
        </div>
        <!-- Feature Card -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
          <div class="text-png-red text-4xl mb-4">👤</div>
          <h3 class="text-xl font-bold mb-4">Employee Portal</h3>
          <p class="text-gray-600">Access your personal dashboard and manage your services.</p>
        </div>
        <!-- Feature Card -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
          <div class="text-png-red text-4xl mb-4">🏢</div>
          <h3 class="text-xl font-bold mb-4">Employer Portal</h3>
          <p class="text-gray-600">Manage employee data and approvals efficiently.</p>
        </div>
        <!-- Feature Card -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
          <div class="text-png-red text-4xl mb-4">📨</div>
          <h3 class="text-xl font-bold mb-4">Email Approvals</h3>
          <p class="text-gray-600">Approve and manage requests directly via email.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section id="how-it-works" class="bg-gradient-to-b from-gray-50 to-gray-100 py-20">
    <div class="container mx-auto px-6">
      <h2 class="text-3xl font-bold text-center mb-12">How It Works</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Step 1 -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
          <div class="text-png-red text-4xl mb-4">1</div>
          <h3 class="text-xl font-bold mb-4">Enter File Number</h3>
          <p class="text-gray-600">Enter your file number in the search bar and click "Find Me".</p>
        </div>
        <!-- Step 2 -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
          <div class="text-png-red text-4xl mb-4">2</div>
          <h3 class="text-xl font-bold mb-4">Verify Identity</h3>
          <p class="text-gray-600">Enter your password to access your profile summary.</p>
        </div>
        <!-- Step 3 -->
        <div class="bg-white p-8 rounded-lg shadow-lg text-center">
          <div class="text-png-red text-4xl mb-4">3</div>
          <h3 class="text-xl font-bold mb-4">Access Services</h3>
          <p class="text-gray-600">Navigate your dashboard to access payslips, letters, and more.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Admin Login Section -->
  <section id="login" class="py-20 gradient-bg">
    <div class="container mx-auto px-6">
      <h2 class="text-4xl font-bold text-center mb-4 text-png-gold">Admin Login</h2>
      <p class="text-center text-white/80 mb-12 max-w-2xl mx-auto">Secure access for system administrators</p>
      <div class="max-w-md mx-auto bg-white p-8 rounded-lg shadow-lg border-2 border-png-gold">
        <form class="space-y-6" action="<?= base_url('login') ?>" method="post">
          <?= csrf_field() ?>
          <div>
            <label class="block text-gray-700 mb-2 font-medium" for="username">Username</label>
            <input
              type="text"
              id="username"
              name="username"
              class="w-full px-4 py-3 border rounded-lg focus:outline-none focus:border-png-red focus:ring-2 focus:ring-png-red/20"
              placeholder="Enter your username"
              required />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium" for="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              class="w-full px-4 py-3 border rounded-lg focus:outline-none focus:border-png-red focus:ring-2 focus:ring-png-red/20"
              placeholder="Enter your password"
              required />
          </div>
          <div>
            <button type="submit" class="w-full bg-png-red text-white py-3 rounded-lg hover:bg-red-700 transition duration-300 flex items-center justify-center">
              <span>Login</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-png-black text-white py-10">
    <div class="container mx-auto px-6 text-center">
      <p class="text-lg">&copy; 2024 GovPSS. All rights reserved.</p>
      <p class="text-sm mt-2">Designed and Developed by <a href="https://www.dakoiims.com" class="text-png-gold hover:text-yellow-400">Dakoii Systems</a></p>
      <p class="text-sm mt-2">Version 1.0</p>
    </div>
  </footer>

  <!-- Smooth Scroll Script -->
  <script>
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
          behavior: 'smooth'
        });
      });
    });
  </script>

  <!-- Add this right before the closing </body> tag, after your existing scripts -->
  <script>
    // Handle session messages
    <?php if (session()->getFlashdata('error')) : ?>
      Swal.fire({
        title: 'Error!',
        text: '<?= session()->getFlashdata('error') ?>',
        icon: 'error',
        confirmButtonColor: '#CE1126',
        confirmButtonText: 'OK'
      });
    <?php endif; ?>

    <?php if (session()->getFlashdata('success')) : ?>
      Swal.fire({
        title: 'Success!',
        text: '<?= session()->getFlashdata('success') ?>',
        icon: 'success',
        confirmButtonColor: '#CE1126',
        confirmButtonText: 'OK'
      });
    <?php endif; ?>
  </script>

  <!-- Page Loading Scripts -->
  <script>
    // Handle page loading
    window.addEventListener('load', function() {
      const loader = document.querySelector('.loader-wrapper');
      loader.classList.add('loader-hidden');
    });

    // Handle navigation loading
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function() {
        const loader = document.querySelector('.loader-wrapper');
        loader.classList.remove('loader-hidden');
        setTimeout(() => {
          loader.classList.add('loader-hidden');
        }, 500);
      });
    });

    // Handle form submissions
    document.querySelectorAll('form').forEach(form => {
      form.addEventListener('submit', function() {
        const loader = document.querySelector('.loader-wrapper');
        loader.classList.remove('loader-hidden');
      });
    });
  </script>

  <!-- PWA Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('<?= base_url() ?>public/sw.js')
          .then(registration => {
            console.log('ServiceWorker registration successful');
          })
          .catch(err => {
            console.log('ServiceWorker registration failed: ', err);
          });
      });
    }
  </script>
</body>

</html>