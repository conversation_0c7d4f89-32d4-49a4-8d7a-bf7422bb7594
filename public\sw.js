const CACHE_NAME = 'govpss-cache-v2';
const OFFLINE_URL = 'offline.html';

const urlsToCache = [
  '/',
  'offline.html',
  'assets/icons/icon-192x192.png',
  'assets/icons/icon-512x512.png',
  'assets/system_img/favicon.ico',
  'assets/css/tailwind.min.css',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
  'https://fonts.googleapis.com/icon?family=Material+Icons',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
  'https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css',
  'https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        // Cache each URL individually and handle errors
        const cachePromises = urlsToCache.map(url => {
          return fetch(url)
            .then(response => {
              if (!response.ok) {
                throw new Error(`Failed to fetch ${url}`);
              }
              return cache.put(url, response);
            })
            .catch(error => {
              console.error(`Failed to cache ${url}: ${error.message}`);
              // Continue caching even if one item fails
              return Promise.resolve();
            });
        });
        
        // Add offline page
        cachePromises.push(
          fetch(OFFLINE_URL)
            .then(response => {
              if (!response.ok) {
                throw new Error(`Failed to fetch ${OFFLINE_URL}`);
              }
              return cache.put(OFFLINE_URL, response);
            })
            .catch(error => {
              console.error(`Failed to cache ${OFFLINE_URL}: ${error.message}`);
              return Promise.resolve();
            })
        );
        
        return Promise.all(cachePromises);
      })
  );
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  self.clients.claim();
});

self.addEventListener('fetch', (event) => {
  if (event.request.mode === 'navigate' || 
      (event.request.method === 'GET' && 
       event.request.headers.get('accept')?.includes('text/html'))) {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          return caches.match(OFFLINE_URL);
        })
    );
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          return response;
        }
        return fetch(event.request)
          .then((response) => {
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }
            
            try {
              const responseToCache = response.clone();
              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(event.request, responseToCache)
                    .catch(err => console.error('Error caching response:', err));
                })
                .catch(err => console.error('Error opening cache:', err));
            } catch (error) {
              console.error('Error in caching logic:', error);
            }
            
            return response;
          })
          .catch(error => {
            console.error('Fetch error:', error);
            return new Response('Network error', { status: 503, statusText: 'Service Unavailable' });
          });
      })
  );
});
  