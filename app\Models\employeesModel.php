<?php namespace App\Models;

use CodeIgniter\Model;

class employeesModel extends Model
{
    protected $table = 'employees';
    protected $primaryKey = 'emp_id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'org_id',
        'fileno',
        'password',
        'fname',
        'lname',
        'gender',
        'dobirth',
        'commence_date',
        'id_photo',
        'phone',
        'primary_email',
        'other_contacts',
        'status',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Callbacks
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    protected function hashPassword(array $data)
    {
        if (!isset($data['data']['password'])) {
            return $data;
        }

        $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        return $data;
    }

    // Custom Methods
    public function findByFileNo($fileno)
    {
        return $this->where('fileno', $fileno)
                    ->first();
    }

    public function getActiveEmployees()
    {
        return $this->where('status', 'active')
                    ->findAll();
    }

    public function getEmployeeFullName($emp_id)
    {
        $employee = $this->find($emp_id);
        if ($employee) {
            return $employee['fname'] . ' ' . $employee['lname'];
        }
        return null;
    }

    /**
     * Get employee with position and group details
     *
     * @param string $field Field to search by (emp_id or fileno)
     * @param string $value Value to search for
     * @return array|null Employee data with position and group details
     */
    public function getEmployeeWithDetails($field, $value)
    {
        return $this->select('employees.*, positions.designation as position_name, positions.appointment_type, groupings.name as group_name')
            ->join('positions', 'positions.employee_id = employees.emp_id', 'left')
            ->join('groupings', 'groupings.id = positions.group_id', 'left')
            ->where('employees.' . $field, $value)
            ->first();
    }
}