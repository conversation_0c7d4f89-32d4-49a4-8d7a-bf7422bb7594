<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white py-3">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0 fw-bold">Appoint Employee to Position</h5>
                    </div>
                    <div class="col-auto">
                        <a href="<?= base_url('appointments') ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i> Back to Appointments
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (session()->has('error')): ?>
                    <div class="alert alert-danger">
                        <?= session('error') ?>
                    </div>
                <?php endif; ?>
                
                <?php if (session()->has('success')): ?>
                    <div class="alert alert-success">
                        <?= session('success') ?>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('positions/appoint') ?>" method="post">
                    <?= csrf_field() ?>
                    <input type="hidden" name="position_id" value="<?= $position['id'] ?>">
                    
                    <div class="mb-3">
                        <label for="position_details" class="form-label">Position Details</label>
                        <input type="text" class="form-control" id="position_details" value="<?= esc($position['position_code'] . ' - ' . $position['designation']) ?>" readonly>
                    </div>
                    
                    <?php if (!empty($position['employee_id'])): ?>
                    <div class="alert alert-info mb-4">
                        <h5><i class="fas fa-info-circle me-2"></i>Current Appointee</h5>
                        <p class="mb-0">
                            This position is currently appointed to 
                            <strong><?= esc($position['fname'] . ' ' . $position['lname']) ?></strong>.
                        </p>
                        <hr>
                        <p class="mb-2">You can:</p>
                        <ul>
                            <li>Appoint a different employee using the form below</li>
                            <li>Vacate this position by clicking the button below</li>
                        </ul>
                        <a href="<?= base_url('positions/vacate/' . $position['id']) ?>" 
                           class="btn btn-warning mt-2" 
                           onclick="return confirm('Are you sure you want to vacate this position? This will remove the current appointee.');">
                            <i class="fas fa-user-slash me-2"></i> Vacate This Position
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="employee_id" class="form-label">Select Employee *</label>
                        <select class="form-select" id="employee_id" name="employee_id" required>
                            <option value="">Select an employee</option>
                            <?php foreach ($employees as $employee): ?>
                                <option value="<?= $employee['emp_id'] ?>" <?= (isset($position['employee_id']) && $position['employee_id'] == $employee['emp_id']) ? 'selected' : '' ?>>
                                    <?= esc($employee['fname'] . ' ' . $employee['lname']) ?> (<?= esc($employee['fileno']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="appointment_type" class="form-label">Appointment Type *</label>
                        <select class="form-select" id="appointment_type" name="appointment_type" required>
                            <option value="">Select appointment type</option>
                           
                            <option value="Acting" <?= (isset($position['appointment_type']) && $position['appointment_type'] == 'Acting') ? 'selected' : '' ?>>Acting</option>
                            <option value="Substantive" <?= (isset($position['appointment_type']) && $position['appointment_type'] == 'Substantive') ? 'selected' : '' ?>>Substantive</option>
                            <option value="Caretaker" <?= (isset($position['appointment_type']) && $position['appointment_type'] == 'Caretaker') ? 'selected' : '' ?>>Caretaker</option>
                            <option value="Probationary" <?= (isset($position['appointment_type']) && $position['appointment_type'] == 'Probationary') ? 'selected' : '' ?>>Probationary</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="appointment_date" class="form-label">Appointment Date</label>
                        <input type="date" class="form-control" id="appointment_date" name="appointment_date" value="<?= isset($position['appointment_date']) ? date('Y-m-d', strtotime($position['appointment_date'])) : '' ?>">
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Save Appointment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Select2 for employee dropdown with bootstrap-5 theme
        $('#employee_id').select2({
            theme: 'bootstrap-5',
            placeholder: "Select an employee",
            allowClear: true
        });
    });
</script>
<?= $this->endSection() ?> 