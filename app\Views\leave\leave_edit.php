<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<!-- Employee Info Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px; font-size: 20px;">
                        <?= strtoupper(substr($employee['fname'], 0, 1) . substr($employee['lname'], 0, 1)) ?>
                    </div>
                    <div>
                        <h5 class="mb-1"><?= esc($employee['fname'] . ' ' . $employee['lname']) ?></h5>
                        <div class="text-muted">
                            <span class="badge bg-secondary me-2">File No: <?= esc($employee['fileno']) ?></span>
                            <?php if (!empty($employee['position_name'])): ?>
                                <span class="badge bg-info me-2"><?= esc($employee['position_name']) ?></span>
                            <?php endif; ?>
                            <?php if (!empty($employee['group_name'])): ?>
                                <span class="badge bg-success"><?= esc($employee['group_name']) ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Leave Form -->
<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Edit Leave Record
                </h5>
            </div>
            <div class="card-body">
                <form action="<?= base_url('leave/update/' . $leave['id']) ?>" method="post" id="leaveForm">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="leave_type" class="form-label">
                                <i class="fas fa-tag me-1"></i>Leave Type <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="leave_type" name="leave_type" required>
                                <option value="">Select Leave Type</option>
                                <option value="Annual Leave" <?= ($leave['leave_type'] == 'Annual Leave') ? 'selected' : '' ?>>Annual Leave</option>
                                <option value="Sick Leave" <?= ($leave['leave_type'] == 'Sick Leave') ? 'selected' : '' ?>>Sick Leave</option>
                                <option value="Maternity Leave" <?= ($leave['leave_type'] == 'Maternity Leave') ? 'selected' : '' ?>>Maternity Leave</option>
                                <option value="Paternity Leave" <?= ($leave['leave_type'] == 'Paternity Leave') ? 'selected' : '' ?>>Paternity Leave</option>
                                <option value="Compassionate Leave" <?= ($leave['leave_type'] == 'Compassionate Leave') ? 'selected' : '' ?>>Compassionate Leave</option>
                                <option value="Study Leave" <?= ($leave['leave_type'] == 'Study Leave') ? 'selected' : '' ?>>Study Leave</option>
                                <option value="Leave Without Pay" <?= ($leave['leave_type'] == 'Leave Without Pay') ? 'selected' : '' ?>>Leave Without Pay</option>
                                <option value="Other" <?= ($leave['leave_type'] == 'Other') ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date_from" class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>Start Date <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?= esc($leave['date_from']) ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_to" class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>End Date <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?= esc($leave['date_to']) ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-clock me-1"></i>Duration
                            </label>
                            <div class="form-control bg-light" id="duration_display">
                                <?php 
                                $start = new DateTime($leave['date_from']);
                                $end = new DateTime($leave['date_to']);
                                $interval = $start->diff($end);
                                $days = $interval->days + 1;
                                ?>
                                <span class="badge bg-success"><?= $days ?> day<?= $days > 1 ? 's' : '' ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="remarks" class="form-label">
                            <i class="fas fa-comment me-1"></i>Remarks
                        </label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="4" 
                                  placeholder="Enter any additional remarks or notes about this leave..."><?= esc($leave['remarks']) ?></textarea>
                        <div class="form-text">Optional: Add any relevant details about the leave</div>
                    </div>
                    
                    <!-- Leave Record Info -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-plus me-1"></i>
                                    <strong>Created:</strong> <?= date('M d, Y H:i', strtotime($leave['created_at'])) ?>
                                </small>
                            </div>
                            <?php if (!empty($leave['updated_at']) && $leave['updated_at'] != $leave['created_at']): ?>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-edit me-1"></i>
                                    <strong>Last Updated:</strong> <?= date('M d, Y H:i', strtotime($leave['updated_at'])) ?>
                                </small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('leave/employee/' . $employee['emp_id']) ?>" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-1"></i>Update Leave Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Calculate duration when dates change
    function calculateDuration() {
        const startDate = $('#date_from').val();
        const endDate = $('#date_to').val();
        
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            if (end >= start) {
                const timeDiff = end.getTime() - start.getTime();
                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // Include both start and end dates
                
                $('#duration_display').html(
                    '<span class="badge bg-success">' + daysDiff + ' day' + (daysDiff > 1 ? 's' : '') + '</span>'
                );
                
                // Update end date minimum
                $('#date_to').attr('min', startDate);
            } else {
                $('#duration_display').html('<span class="text-danger">End date must be after start date</span>');
            }
        } else {
            $('#duration_display').html('<span class="text-muted">Select dates to calculate duration</span>');
        }
    }
    
    $('#date_from, #date_to').on('change', calculateDuration);
    
    // Form validation
    $('#leaveForm').on('submit', function(e) {
        const startDate = $('#date_from').val();
        const endDate = $('#date_to').val();
        
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            if (end < start) {
                e.preventDefault();
                Swal.fire({
                    title: 'Invalid Date Range',
                    text: 'End date must be after or equal to start date.',
                    icon: 'error',
                    confirmButtonColor: '#CE1126'
                });
                return false;
            }
        }
    });
    
    // Auto-focus first field
    $('#leave_type').focus();
});
</script>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--png-red);
    box-shadow: 0 0 0 0.25rem rgba(206, 17, 38, 0.25);
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

.badge {
    font-size: 0.75rem;
}

#duration_display {
    display: flex;
    align-items: center;
    min-height: calc(1.5em + 1.5rem + 2px);
}

.text-danger {
    color: #dc3545 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #004085;
    border-radius: 8px;
}
</style>

<?= $this->endSection() ?>
