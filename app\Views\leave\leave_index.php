<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Employee Leave Management
                    </h5>
                    <small class="text-light">
                        <i class="fas fa-info-circle me-1"></i>
                        Click "View Leave" to manage employee leave records
                    </small>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="employeesTable" class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>File No.</th>
                                <th>Employee Name</th>
                                <th>Position</th>
                                <th>Group/Department</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($employees)): ?>
                                <?php foreach ($employees as $employee): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?= esc($employee['fileno']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2"
                                                     style="width: 35px; height: 35px; font-size: 14px;">
                                                    <?= strtoupper(substr($employee['fname'], 0, 1) . substr($employee['lname'], 0, 1)) ?>
                                                </div>
                                                <div>
                                                    <strong><?= esc($employee['fname'] . ' ' . $employee['lname']) ?></strong>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if (!empty($employee['designation'])): ?>
                                                <span class="badge bg-info text-dark">
                                                    <?= esc($employee['designation']) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-minus"></i> No Position
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($employee['group_name'])): ?>
                                                <span class="badge bg-success">
                                                    <?= esc($employee['group_name']) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-minus"></i> No Group
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($employee['status'] == 'active'): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle me-1"></i>Active
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times-circle me-1"></i>Inactive
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('leave/employee/' . $employee['emp_id']) ?>" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-calendar-check me-1"></i>
                                                View Leave
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-2x mb-3 d-block"></i>
                                        <p class="mb-0">No active employees found</p>
                                        <small>Add employees first to manage their leave records</small>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTables Initialization -->
<script>
$(document).ready(function() {
    $('#employeesTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'asc']], // Sort by employee name
        columnDefs: [
            {
                targets: [5], // Actions column
                orderable: false,
                searchable: false
            }
        ],
        language: {
            search: "Search employees:",
            lengthMenu: "Show _MENU_ employees per page",
            info: "Showing _START_ to _END_ of _TOTAL_ employees",
            infoEmpty: "No employees found",
            infoFiltered: "(filtered from _MAX_ total employees)",
            emptyTable: "No employees available for leave management",
            zeroRecords: "No matching employees found"
        },
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Export Excel',
                className: 'btn btn-success btn-sm',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4] // Exclude actions column
                }
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> Export PDF',
                className: 'btn btn-danger btn-sm',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4] // Exclude actions column
                }
            }
        ]
    });
});
</script>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.badge {
    font-size: 0.75rem;
}

.btn-sm {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
}

.table-responsive {
    border-radius: 8px;
}

/* DataTables custom styling */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.dataTables_wrapper .dataTables_filter input:focus {
    border-color: var(--png-red);
    box-shadow: 0 0 0 0.25rem rgba(206, 17, 38, 0.25);
}

.page-link {
    border-radius: 6px;
    margin: 0 2px;
}

.page-item.active .page-link {
    background-color: var(--png-red);
    border-color: var(--png-red);
}
</style>

<?= $this->endSection() ?>
