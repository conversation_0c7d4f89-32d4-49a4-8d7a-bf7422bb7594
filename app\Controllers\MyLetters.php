<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\LettersModel;
use App\Models\orgModel;
use setasign\Fpdi\Tcpdf\Fpdi;

// Custom PDF class with header and footer
class CustomLetterPDF extends Fpdi
{
    protected $orgName;
    protected $orgLogo;
    protected $baseUrl;
    protected $orgAddress;
    protected $orgPhones;
    protected $orgEmails;
    protected $letter_confirmed_at;
    protected $letter_file_code;
    public function setOrgDetails($orgName, $orgLogo, $baseUrl, $orgAddress = '', $orgPhones = '', $orgEmails = '', $letter_confirmed_at = '', $letter_file_code = '')
    {
        $this->orgName = $orgName;
        $this->orgLogo = $orgLogo;
        $this->baseUrl = $baseUrl;
        $this->orgAddress = $orgAddress;
        $this->orgPhones = $orgPhones;
        $this->orgEmails = $orgEmails;
        $this->letter_confirmed_at = $letter_confirmed_at;
        $this->letter_file_code = $letter_file_code;
    }

    public function Header()
    {
        // Add logo
        if (file_exists($this->orgLogo)) {
            $this->Image($this->orgLogo, 90, 5, 30);
        }


        // Organization Name
        $this->SetFont('helvetica', 'B', 16);
        $this->SetXY(0, 20);
        $this->Cell(210, 22, strtoupper($this->orgName), 0, 1, 'C');

        // Set green color for text (RGB values for a professional dark green)
        $this->SetTextColor(0, 100, 0);

        // Human Resource Management
        $this->SetFont('helvetica', 'B', 14);
        $this->SetXY(0, 28);
        $this->Cell(210, 22, strtoupper("HUMAN RESOURCE MANAGEMENT"), 0, 1, 'C');

        // Reset text color to black for the rest of the document
        $this->SetTextColor(0, 0, 0);

        // Add a line
        $this->SetLineWidth(0.5);
        $this->Line(10, 45, 200, 45);

        // Reset line width
        $this->SetLineWidth(0.2);

        // Add some space after header
        $this->Ln(5);
    }

    public function Footer()
    {
        // QR Code - Generate timestamp and URL
        $qrData = "Approved: " . date('d-m-Y', strtotime($this->letter_confirmed_at)) . "\nURL: " . $this->baseUrl . "\nFile Code: " . $this->letter_file_code;

        // Set style for QR code
        $style = array(
            'border' => false,
            'vpadding' => 0,
            'hpadding' => 0,
            'fgcolor' => array(0, 0, 0),
            'bgcolor' => false,
            'module_width' => 3,
            'module_height' => 3
        );

        // Position for QR code (far right)
        $this->SetY(-43);
        $this->SetX(170); // Moved X position further right
        $this->write2DBarcode($qrData, 'QRCODE,L', '', '', 25, 25, $style);

        // Footer content starts at 25mm from bottom
        $this->SetY(-25);

        // Line above footer
        $this->SetDrawColor(0, 100, 0); // Dark green color
        $this->Line(10, $this->GetY(), 200, $this->GetY());

        // Reset colors
        $this->SetTextColor(68, 68, 68); // Dark gray for text

        // Contact information
        $this->SetFont('helvetica', '', 11);
        // Address
        if (!empty($this->orgAddress)) {
            $this->Cell(0, 10, $this->orgAddress, 0, 1, 'C');
        }

        // Set Y position for phones and emails
        $currentY = $this->GetY();

        // Phones on left
        if (!empty($this->orgPhones)) {
            $this->SetXY(10, $currentY);
            $this->Cell(95, 4, 'Tel: ' . $this->orgPhones, 0, 1, 'L'); // Print each line

        }

        // Emails on right
        if (!empty($this->orgEmails)) {
            $this->SetXY(105, $currentY);
            $this->Cell(95, 4, 'Email: ' . $this->orgEmails, 0, 1, 'R');
        }

        // Set Y position to very bottom of page
        $this->SetY(-5);

        // Generation info at bottom
        $this->SetFont('helvetica', 'I', 7);
        $this->Cell(0, 1, 'Generated from GovPSS (' . $this->baseUrl . ') @ ' . date('d-m-Y H:i'), 0, 0, 'C');
    }
}

class MyLetters extends BaseController
{
    protected $lettersModel;
    protected $orgModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'text']);
        $this->lettersModel = new LettersModel();
        $this->orgModel = new orgModel();
        $this->session = session();
    }

    public function view()
    {
        if (!$this->session->get('isEmployeeLoggedIn')) {
            return redirect()->to('/');
        }

        $data = [
            'title' => 'My Letters',
            'letters' => $this->lettersModel->where('created_by', $this->session->get('emp_id'))
                ->orderBy('created_at', 'DESC')
                ->findAll()
        ];

        return view('employee_portal/my_letters', $data);
    }

    public function request_confirmation()
    {
        try {
            if (!$this->session->get('isEmployeeLoggedIn')) {
                throw new \Exception('Please login first');
            }

            $emp_id = $this->session->get('emp_id');

            // Validate address
            $address = $this->request->getPost('address');
            if (empty($address)) {
                throw new \Exception('Address is required');
            }

            // Get employee details with organization info
            $db = \Config\Database::connect();
            $employee = $db->table('employees')
                ->select('employees.*, dakoii_org.name as org_name, dakoii_org.id as org_id, dakoii_org.emails as org_emails, positions.designation, groupings.name as group_name')
                ->join('dakoii_org', 'dakoii_org.id = employees.org_id', 'left')
                ->join('positions', 'positions.employee_id = employees.emp_id', 'left')
                ->join('groupings', 'groupings.id = positions.group_id', 'left')
                ->where('employees.emp_id', $emp_id)
                ->get()
                ->getRowArray();

            if (!$employee) {
                throw new \Exception('Employee details not found');
            }

            if (empty($employee['org_id'])) {
                throw new \Exception('Employee organization not found');
            }

            // Check if organization has email configured
            if (empty($employee['org_emails'])) {
                throw new \Exception('Organization email not configured. Please contact your administrator.');
            }

            // Get first email from the list
            $emails = explode(',', $employee['org_emails']);
            $toEmail = trim($emails[0]);

            if (empty($toEmail)) {
                throw new \Exception('No valid organization email found');
            }

            // Generate letter content
            $content = "This letter serves to confirm that " . $employee['fname'] . " " . $employee['lname'] .
                " (File No: " . $employee['fileno'] . ") is currently employed with " . $employee['org_name'] .
                " as " . ($employee['designation'] ?? 'an employee') . " in the " . ($employee['group_name'] ?? 'organization') .
                ".\n\n" .
                // $employee['fname'] . " commenced employment with us on " . date('d F Y', strtotime($employee['commence_date'])) .
                // " and continues to be employed on a permanent basis.\n\n" .
                $employee['fname'] . " is currently not on Rec-Leave or Furlough \n\n" .
                "Should you require any additional information, please do not hesitate to contact our office.";

            $letter_file_code = $this->lettersModel->generateUniqueLetterFileCode('HR-EMP');

            // Prepare letter data
            $letterData = [
                'letter_type' => 'confirmation',
                'created_by' => $emp_id,
                'org_id' => $employee['org_id'],
                'address_to' => $address,
                'subject' => 'CONFIRMATION OF EMPLOYMENT FOR ' . strtoupper($employee['fname']) . ' ' . strtoupper($employee['lname']),
                'content' => $content,
                'confirmation_status' => 'pending',
                'unique_code' => md5(uniqid(rand(), true)),
                'letter_file_code' => $letter_file_code,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Insert into database
            $lettersModel = new \App\Models\LettersModel();
            if (!$lettersModel->insert($letterData)) {
                $error = $lettersModel->errors();
                throw new \Exception('Database Error: ' . json_encode($error));
            }

            // Get organization emails
            $orgModel = new \App\Models\orgModel();
            $orgDetails = $orgModel->find($employee['org_id']);
            if ($orgDetails && !empty($orgDetails['emails'])) {
                $emails = explode(',', $orgDetails['emails']);
                $toEmail = trim($emails[0]);

                // Send email notification
                $this->sendLetterEmail($employee, $letterData['unique_code'], $toEmail);
            }

            return redirect()->to('employee_portal/my_letters')
                ->with('success', 'Confirmation letter has been created successfully');
        } catch (\Exception $e) {
            log_message('error', 'Error creating confirmation letter: ' . $e->getMessage());
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    private function generateConfirmationLetterPDF($employee, $letter_id)
    {
        // Get organization details including signature and stamp
        $orgDetails = $this->orgModel->find($employee['org_id']);
        if (!$orgDetails) {
            throw new \Exception('Organization details not found');
        }

        // Get letter content from database
        $letter = $this->lettersModel->find($letter_id);

        if (!$letter) {
            throw new \Exception('Letter not found or not approved');
        }

        // Create PDF
        $pdf = new CustomLetterPDF('P', 'mm', 'A4');

        // Set document information
        $pdf->SetCreator('GovPSS');
        $pdf->SetAuthor($employee['fname'] . ' ' . $employee['lname']);
        $pdf->SetTitle('Confirmation Letter');

        // Set margins
        $pdf->SetMargins(25, 50, 25);
        $pdf->SetAutoPageBreak(true, 25);

        // Set organization details for header and footer
        $pdf->setOrgDetails(
            $employee['org_name'] ?? 'Organization Name',
            FCPATH . ($employee['org_logo'] ?? 'public/assets/system_img/system-logo.png'),
            base_url(),
            $orgDetails['postal_address'] ?? '',
            $orgDetails['phones'] ?? '',
            $orgDetails['emails'] ?? '',
            $letter['confirmed_at'] ?? '',
            $letter['letter_file_code'] ?? ''
        );

        // Add a page
        $pdf->AddPage();

        // Add date
        $pdf->SetFont('helvetica', '', 11);
        $pdf->Cell(0, 10, date('d F Y', strtotime($letter['confirmed_at'])), 0, 1, 'R');
        $pdf->Ln(2);

        // Letter File Code
        $pdf->SetFont('helvetica', '', 11);
        $pdf->Cell(0, 2, 'File Code: ' . $letter['letter_file_code'], 0, 1, 'R');
        $pdf->Ln(2);

        // Add address
        $pdf->SetFont('helvetica', '', 11);
        $pdf->MultiCell(0, 1, $letter['address_to'], 0, 'L');
        $pdf->Ln(10);

        // Add salutation
        $pdf->Cell(0, 10, 'Dear Sir/Madam,', 0, 1, 'L');
        $pdf->Ln(5);

        // Add subject
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->Cell(0, 10, $letter['subject'], 0, 1, 'L');
        $pdf->Ln(5);

        // Add content from database
        $pdf->SetFont('helvetica', '', 11);
        $pdf->MultiCell(0, 5, $letter['content'], 0, 'J');
        $pdf->Ln(10);

        // Add closing
        $pdf->Cell(0, 10, 'Yours faithfully,', 0, 1, 'L');
        $pdf->Ln(15);

        // Add signature and stamp if available
        if (!empty($orgDetails['signature_filepath']) && file_exists(FCPATH . $orgDetails['signature_filepath'])) {
            $pdf->Image(
                FCPATH . $orgDetails['signature_filepath'],
                25,
                $pdf->GetY() - 10,
                50
            );
        }

        if (!empty($orgDetails['stamp_filepath']) && file_exists(FCPATH . $orgDetails['stamp_filepath'])) {
            $pdf->Image(
                FCPATH . $orgDetails['stamp_filepath'],
                60,
                $pdf->GetY() - 22,
                50
            );
        }

        if (!empty($orgDetails['approved_stamp_filepath']) && file_exists(FCPATH . $orgDetails['approved_stamp_filepath'])) {
            $pdf->Image(
                FCPATH . $orgDetails['approved_stamp_filepath'],
                130,
                $pdf->GetY() - 22,
                50
            );
        }

        $pdf->SetDrawColor(0);
        $pdf->SetTextColor(0);
        $pdf->Ln(5);

        $pdf->SetFont('helvetica', '', 11);
        $pdf->Cell(0, 10, $orgDetails['signature_name'] ?? '', 0, 1, 'L');
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->Cell(0, 10, $orgDetails['signature_position'] ?? 'Human Resources Manager', 0, 1, 'L');

        return $pdf;
    }

    private function sendLetterEmail($employee, $unique_code, $toEmail)
    {
        try {
            // Check for empty email
            if (empty($toEmail)) {
                throw new \Exception('No valid organization email found');
            }

            // Load email service
            $email = \Config\Services::email();

            // Set email parameters
            $email->setFrom('<EMAIL>', 'GovPSS System');
            $email->setTo($toEmail);
            $email->setSubject('Confirmation Letter Request for ' . $employee['fname'] . ' ' . $employee['lname'] . ' - ' . $employee['fileno']);

            // Prepare HTML message
            $message = "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='utf-8'>
                <title>Letter Confirmation Request</title>
            </head>
            <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0;'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <div style='background-color: #CE1126; color: white; padding: 20px; text-align: center;'>
                        <h2 style='margin: 0;'>New Letter Confirmation Request</h2>
                    </div>
                    <div style='padding: 20px; background-color: #f9f9f9;'>
                        <p>A new confirmation letter request has been submitted and requires your attention.</p>
                        <p><strong>Employee:</strong> {$employee['fname']} {$employee['lname']}</p>
                        <p><strong>File Number:</strong> {$employee['fileno']}</p>
                        <p>Please click the button below to review and process the letter:</p>
                        <p style='text-align: center;'>
                            <a href='" . base_url("view_letter/{$unique_code}/{$toEmail}") . "' 
                               style='display: inline-block; padding: 10px 20px; background-color: #CE1126; 
                                      color: white; text-decoration: none; border-radius: 5px;'>
                                View Letter
                            </a>
                        </p>
                        <p style='font-size: 12px;'>
                            If the button doesn't work, copy and paste this link into your browser:<br>
                            " . base_url("view_letter/{$unique_code}/{$toEmail}") . "
                        </p>
                    </div>
                    <div style='text-align: center; font-size: 12px; color: #666; margin-top: 20px;'>
                        <p>This is an automated message from GovPSS System. Please do not reply.</p>
                    </div>
                </div>
            </body>
            </html>";

            $email->setMessage($message);

            // Send email and get debug info
            if (!$email->send()) {
                $debugInfo = $email->printDebugger(['headers', 'subject', 'body']);
                log_message('error', 'Failed to send email. Debug info: ' . print_r($debugInfo, true));
                throw new \Exception('Failed to send email. Please check the system logs for details.');
            }

            log_message('info', 'Email sent successfully to: ' . $toEmail);
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Email Error: ' . $e->getMessage());
            throw $e;
        }
    }

    public function download($id)
    {
        if (!$this->session->get('isEmployeeLoggedIn')) {
            return redirect()->to('/');
        }

        try {
            $letter = $this->lettersModel->find($id);
            if (!$letter) {
                return redirect()->back()->with('error', 'Letter not found');
            }

            if ($letter['created_by'] != $this->session->get('emp_id')) {
                return redirect()->back()->with('error', 'You are not authorized to download this letter');
            }

            if ($letter['confirmation_status'] !== 'approved') {
                return redirect()->back()->with('error', 'Letter must be approved before downloading');
            }

            // Get employee details for PDF generation
            $db = \Config\Database::connect();
            $employee = $db->table('employees')
                ->select('employees.*, dakoii_org.name as org_name, dakoii_org.orglogo as org_logo, dakoii_org.id as org_id, positions.designation, groupings.name as group_name')
                ->join('dakoii_org', 'dakoii_org.id = employees.org_id', 'left')
                ->join('positions', 'positions.employee_id = employees.emp_id', 'left')
                ->join('groupings', 'groupings.id = positions.group_id', 'left')
                ->where('employees.emp_id', $letter['created_by'])
                ->get()
                ->getRowArray();

            // Generate PDF
            $pdf = $this->generateConfirmationLetterPDF($employee, $id);

            // Generate filename for download
            $fileName = 'confirmation_letter_' . $employee['fileno'] . '_' . date('YmdHis') . '.pdf';

            // Output PDF directly to browser for download
            return $this->response
                ->setHeader('Content-Type', 'application/pdf')
                ->setHeader('Content-Disposition', 'attachment; filename="' . $fileName . '"')
                ->setBody($pdf->Output($fileName, 'S'));
        } catch (\Exception $e) {
            log_message('error', 'Error downloading letter: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error downloading letter: ' . $e->getMessage());
        }
    }
}
