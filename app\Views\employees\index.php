<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- Main Content -->
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h4 mb-0 text-gray-800 fw-bold">Employees Management</h2>
        <div class="d-flex gap-2">
            <button onclick="openImportModal()" class="btn btn-primary d-flex align-items-center gap-2">
                <i class="fas fa-file-import"></i>
                <span>Import Employees</span>
            </button>
            <button onclick="openAddModal()" class="btn btn-danger d-flex align-items-center gap-2">
                <i class="fas fa-plus"></i>
                <span>Add Employee</span>
            </button>
        </div>
    </div>

    <!-- Employees Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table id="employeesTable" class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>File No</th>
                            <th>Name</th>
                            <th>Gender</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Employee Modal -->
<div class="modal fade" id="employeeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-bottom">
                <h5 class="modal-title" id="modalTitle">Add Employee</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="employeeForm" onsubmit="saveEmployee(event)">
                    <?= csrf_field() ?>
                    <input type="hidden" id="emp_id" name="emp_id">
                    
                    <!-- Basic Info -->
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label required">File Number</label>
                            <input type="text" id="fileno" name="fileno" class="form-control" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">First Name</label>
                            <input type="text" id="fname" name="fname" class="form-control" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Last Name</label>
                            <input type="text" id="lname" name="lname" class="form-control" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Gender</label>
                            <select id="gender" name="gender" class="form-select" required>
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Date of Birth</label>
                            <input type="date" id="dobirth" name="dobirth" class="form-control">
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Commence Date</label>
                            <input type="date" id="commence_date" name="commence_date" class="form-control">
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label required">Status</label>
                            <select id="status" name="status" class="form-select" required>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>

                    <!-- Contact Details -->
                    <div class="row g-3 mt-3">
                        <div class="col-md-6">
                            <label class="form-label">Phone</label>
                            <input type="tel" id="phone" name="phone" class="form-control">
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Email</label>
                            <input type="email" id="primary_email" name="primary_email" class="form-control">
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Other Contacts</label>
                            <textarea id="other_contacts" name="other_contacts" rows="2" class="form-control"></textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-top">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="employeeForm" class="btn btn-danger" id="saveButton">
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                    <span class="button-text">Save Employee</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-bottom">
                <h5 class="modal-title">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reset the password for <strong id="resetEmployeeName"></strong>?</p>
                <p class="text-muted">The password will be reset to the employee's file number.</p>
            </div>
            <div class="modal-footer border-top">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmResetButton">
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                    <span class="button-text">Reset Password</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Import Employees Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-bottom">
                <h5 class="modal-title">Import Employees</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" onsubmit="importEmployees(event)">
                    <?= csrf_field() ?>
                    <div class="mb-3">
                        <label class="form-label required">CSV File</label>
                        <input type="file" id="csvFile" name="csvFile" class="form-control" accept=".csv" required>
                        <div class="form-text">
                            File should contain: File Number, First Name, Last Name
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-top">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="importForm" class="btn btn-primary" id="importButton">
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                    <span class="button-text">Import Employees</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.required:after {
    content: " *";
    color: red;
}
</style>

<script>
// Wait for jQuery to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if jQuery is loaded
    if (typeof jQuery === 'undefined') {
        console.error('jQuery is not loaded');
        return;
    }

    // Setup CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Add CSRF token to the page
    const csrfToken = '<?= csrf_hash() ?>';
    $('head').append(`<meta name="csrf-token" content="${csrfToken}">`);
    
    // Add hidden CSRF input if not exists
    if ($('input[name="<?= csrf_token() ?>"]').length === 0) {
        $('body').append(`<input type="hidden" name="<?= csrf_token() ?>" value="${csrfToken}">`);
    }

    // Initialize DataTable
    let employeesTable;
    
    try {
        employeesTable = $('#employeesTable').DataTable({
            ajax: {
                url: '<?= base_url('employees/get_employees') ?>',
                dataSrc: 'data'
            },
            columns: [
                { data: 'fileno' },
                { data: 'name' },
                { data: 'gender' },
                { data: 'phone' },
                { data: 'email' },
                { 
                    data: 'status',
                    render: function(data) {
                        return `<span class="badge bg-${data === 'active' ? 'success' : 'danger'}">${data}</span>`;
                    }
                },
                { 
                    data: 'actions',
                    render: function(data, type, row) {
                        return `
                            <div class="btn-group">
                                <button onclick="editEmployee(${row.emp_id})" class="btn btn-link text-primary btn-sm">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="openResetPasswordModal(${row.emp_id}, '${row.name}')" class="btn btn-link text-warning btn-sm">
                                    <i class="fas fa-key"></i>
                                </button>
                            </div>
                        `;
                    }
                }
            ],
            responsive: true,
            order: [[1, 'asc']]
        });
    } catch (error) {
        console.error('Error initializing DataTable:', error);
    }

    // Attach event handlers using jQuery
    $('#employeeModal').on('hidden.bs.modal', function () {
        clearValidation();
        $('#employeeForm')[0].reset();
    });

    // Make functions globally available
    window.openAddModal = function() {
        $('#modalTitle').text('Add Employee');
        $('#employeeForm')[0].reset();
        $('#emp_id').val('');
        clearValidation();
        new bootstrap.Modal('#employeeModal').show();
    };

    window.editEmployee = function(id) {
        $('#modalTitle').text('Edit Employee');
        clearValidation();
        
        $.get(`<?= base_url('employees/get') ?>/${id}`, function(data) {
            console.log('Gender from database:', data.gender); // Debug log
            $('#emp_id').val(data.emp_id);
            $('#fileno').val(data.fileno);
            $('#fname').val(data.fname);
            $('#lname').val(data.lname);
            $('#gender').val(data.gender);
            $('#dobirth').val(data.dobirth);
            $('#commence_date').val(data.commence_date);
            $('#phone').val(data.phone);
            $('#primary_email').val(data.primary_email);
            $('#other_contacts').val(data.other_contacts);
            $('#status').val(data.status);
            
            new bootstrap.Modal('#employeeModal').show();
        });
    };

    window.clearValidation = function() {
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    };

    window.saveEmployee = function(e) {
        e.preventDefault();
        clearValidation();

        const saveButton = document.getElementById('saveButton');
        const spinner = saveButton.querySelector('.spinner-border');
        const buttonText = saveButton.querySelector('.button-text');
        
        spinner.classList.remove('d-none');
        buttonText.textContent = $('#emp_id').val() ? 'Updating...' : 'Saving...';
        saveButton.disabled = true;

        const formData = new FormData(e.target);
        // Add CSRF token to formData
        formData.append('csrf_token', $('input[name="csrf_token"]').val());
        
        const id = $('#emp_id').val();
        const url = id ? `<?= base_url('employees/update') ?>/${id}` : '<?= base_url('employees/save') ?>';

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.status) {
                    $('#employeeModal').modal('hide');
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: response.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                    if (employeesTable) {
                        employeesTable.ajax.reload();
                    }
                    // Update CSRF token after successful request
                    if (response.csrf_token) {
                        $('input[name="csrf_token"]').val(response.csrf_token);
                    }
                } else {
                    Swal.fire('Error', response.message || 'An error occurred', 'error');
                }
            },
            error: function(xhr) {
                if (xhr.status === 403) {
                    Swal.fire('Error', 'Session expired. Please refresh the page.', 'error');
                } else if (xhr.status === 400 && xhr.responseJSON) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(key => {
                        $(`#${key}`).addClass('is-invalid')
                                   .siblings('.invalid-feedback')
                                   .text(errors[key]);
                    });
                } else {
                    Swal.fire('Error', 'An error occurred while saving', 'error');
                }
            },
            complete: function() {
                spinner.classList.add('d-none');
                buttonText.textContent = 'Save Employee';
                saveButton.disabled = false;
            }
        });
    };

    window.openResetPasswordModal = function(empId, empName) {
        $('#resetEmployeeName').text(empName);
        const modal = new bootstrap.Modal('#resetPasswordModal');
        modal.show();

        // Handle reset password confirmation
        $('#confirmResetButton').off('click').on('click', function() {
            const button = $(this);
            const spinner = button.find('.spinner-border');
            const buttonText = button.find('.button-text');

            // Show loading state
            spinner.removeClass('d-none');
            buttonText.text('Resetting...');
            button.prop('disabled', true);

            // Send reset password request
            $.ajax({
                url: `<?= base_url('employees/reset-password') ?>/${empId}`,
                type: 'POST',
                data: {
                    '<?= csrf_token() ?>': $('input[name="<?= csrf_token() ?>"]').val()
                },
                success: function(response) {
                    if (response.status) {
                        $('#resetPasswordModal').modal('hide');
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: response.message,
                            showConfirmButton: false,
                            timer: 1500
                        });
                        // Update CSRF token if provided in response
                        if (response.csrf_hash) {
                            $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                            $('meta[name="csrf-token"]').attr('content', response.csrf_hash);
                        }
                    } else {
                        Swal.fire('Error', response.message || 'An error occurred', 'error');
                    }
                },
                error: function(xhr) {
                    if (xhr.status === 403) {
                        Swal.fire('Error', 'Session expired. Please refresh the page.', 'error');
                    } else {
                        Swal.fire('Error', 'Failed to reset password', 'error');
                    }
                },
                complete: function() {
                    // Reset button state
                    spinner.addClass('d-none');
                    buttonText.text('Reset Password');
                    button.prop('disabled', false);
                }
            });
        });
    };

    window.openImportModal = function() {
        $('#importForm')[0].reset();
        clearValidation();
        new bootstrap.Modal('#importModal').show();
    };

    window.importEmployees = function(e) {
        e.preventDefault();
        clearValidation();

        const importButton = document.getElementById('importButton');
        const spinner = importButton.querySelector('.spinner-border');
        const buttonText = importButton.querySelector('.button-text');
        
        spinner.classList.remove('d-none');
        buttonText.textContent = 'Importing...';
        importButton.disabled = true;

        const formData = new FormData(e.target);
        
        $.ajax({
            url: '<?= base_url('employees/import') ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.status) {
                    $('#importModal').modal('hide');
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: response.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                    if (employeesTable) {
                        employeesTable.ajax.reload();
                    }
                } else {
                    Swal.fire('Error', response.message || 'An error occurred', 'error');
                }
            },
            error: function(xhr) {
                if (xhr.status === 403) {
                    Swal.fire('Error', 'Session expired. Please refresh the page.', 'error');
                } else if (xhr.status === 400 && xhr.responseJSON) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(key => {
                        $(`#${key}`).addClass('is-invalid')
                                   .siblings('.invalid-feedback')
                                   .text(errors[key]);
                    });
                } else {
                    Swal.fire('Error', 'An error occurred while importing', 'error');
                }
            },
            complete: function() {
                spinner.classList.add('d-none');
                buttonText.textContent = 'Import Employees';
                importButton.disabled = false;
            }
        });
    };
});
</script>
<?= $this->endSection() ?> 