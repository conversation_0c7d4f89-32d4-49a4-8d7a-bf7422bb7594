<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\orgModel;
use <PERSON>Igniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

class OrgSettings extends BaseController
{
    protected $orgModel;
    protected $session;
    protected $helpers = ['form', 'url', 'file', 'text', 'html'];

    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Initialize session
        $this->session = \Config\Services::session();

        // Load models
        $this->orgModel = new orgModel();

        // Check if user is logged in
        if (!$this->session->has('logged_in')) {
            header('Location: ' . base_url('login'));
            exit;
        }

        // Check if org_id exists in session
        if (!$this->session->has('org_id')) {
            $this->session->setFlashdata('error', 'Organization ID not found in session.');
            header('Location: ' . base_url('dashboard'));
            exit;
        }
    }

    public function org_settings()
    {
        $data = [
            'title' => 'Organization Settings',
            'menu' => 'org_settings',
            'org_data' => $this->orgModel->where('id', $this->session->get('org_id'))->first()
        ];

        if (empty($data['org_data'])) {
            $this->session->setFlashdata('error', 'Organization not found.');
            return redirect()->to(base_url('dashboard'));
        }

        return view('admin/org_settings', $data);
    }

    public function update()
    {
        // Get the organization record
        $org = $this->orgModel->where('id', $this->session->get('org_id'))->first();
        if (!$org) {
            return redirect()->back()->with('error', 'Organization record not found.');
        }

        // Handle logo upload
        $logo = $this->request->getFile('orglogo');
        $logoName = $org['orglogo']; // Keep existing logo by default

        if ($logo->isValid() && !$logo->hasMoved()) {
            // Validate file size (10MB max)
            if ($logo->getSizeByUnit('mb') > 10) {
                return redirect()->back()->with('error', 'Logo file size must not exceed 10MB.');
            }

            // Validate file type
            if (!in_array($logo->getExtension(), ['jpg', 'jpeg', 'png', 'gif'])) {
                return redirect()->back()->with('error', 'Invalid logo file type. Only JPG, PNG, and GIF are allowed.');
            }

            // Delete old logo if exists
            if (!empty($org['orglogo']) && file_exists($org['orglogo'])) {
                unlink($org['orglogo']);
            }

            // Generate new filename
            $logoName = 'public/uploads/org_logo/' . $org['orgcode'] . '_' . time() . '.' . $logo->getExtension();

            // Move file to uploads directory
            if (!is_dir('public/uploads/org_logo')) {
                mkdir('public/uploads/org_logo', 0777, true);
            }
            $logo->move(ROOTPATH . 'public/uploads/org_logo', basename($logoName));
        }

        // Handle signature upload
        $signature = $this->request->getFile('signature_filepath');
        $signatureName = $org['signature_filepath']; // Keep existing signature by default

        if ($signature->isValid() && !$signature->hasMoved()) {
            // Validate file size (10MB max)
            if ($signature->getSizeByUnit('mb') > 10) {
                return redirect()->back()->with('error', 'Signature file size must not exceed 10MB.');
            }

            // Validate file type
            if (!in_array($signature->getExtension(), ['jpg', 'jpeg', 'png'])) {
                return redirect()->back()->with('error', 'Invalid signature file type. Only JPG and PNG are allowed.');
            }

            // Delete old signature if exists
            if (!empty($org['signature_filepath']) && file_exists($org['signature_filepath'])) {
                unlink($org['signature_filepath']);
            }

            // Generate new filename
            $signatureName = 'public/uploads/org_signatures/' . $org['orgcode'] . '_' . time() . '.' . $signature->getExtension();

            // Move file to uploads directory
            if (!is_dir('public/uploads/org_signatures')) {
                mkdir('public/uploads/org_signatures', 0777, true);
            }
            $signature->move(ROOTPATH . 'public/uploads/org_signatures', basename($signatureName));
        }

        // Handle stamp upload
        $stamp = $this->request->getFile('stamp_filepath');
        $stampName = $org['stamp_filepath']; // Keep existing stamp by default

        if ($stamp->isValid() && !$stamp->hasMoved()) {
            // Validate file size (10MB max)
            if ($stamp->getSizeByUnit('mb') > 10) {
                return redirect()->back()->with('error', 'Stamp file size must not exceed 10MB.');
            }

            // Validate file type
            if (!in_array($stamp->getExtension(), ['jpg', 'jpeg', 'png'])) {
                return redirect()->back()->with('error', 'Invalid stamp file type. Only JPG and PNG are allowed.');
            }

            // Delete old stamp if exists
            if (!empty($org['stamp_filepath']) && file_exists($org['stamp_filepath'])) {
                unlink($org['stamp_filepath']);
            }

            // Generate new filename
            $stampName = 'public/uploads/org_stamps/' . $org['orgcode'] . '_' . time() . '.' . $stamp->getExtension();

            // Move file to uploads directory
            if (!is_dir('public/uploads/org_stamps')) {
                mkdir('public/uploads/org_stamps', 0777, true);
            }
            $stamp->move(ROOTPATH . 'public/uploads/org_stamps', basename($stampName));
        }

        // Handle approval stamp upload
        $approvalStamp = $this->request->getFile('approved_stamp_filepath');
        $approvalStampName = $org['approved_stamp_filepath']; // Keep existing approval stamp by default

        if ($approvalStamp->isValid() && !$approvalStamp->hasMoved()) {
            // Validate file size (10MB max)
            if ($approvalStamp->getSizeByUnit('mb') > 10) {
                return redirect()->back()->with('error', 'Approval stamp file size must not exceed 10MB.');
            }

            // Validate file type
            if (!in_array($approvalStamp->getExtension(), ['jpg', 'jpeg', 'png'])) {
                return redirect()->back()->with('error', 'Invalid approval stamp file type. Only JPG and PNG are allowed.');
            }

            // Delete old approval stamp if exists
            if (!empty($org['approved_stamp_filepath']) && file_exists($org['approved_stamp_filepath'])) {
                unlink($org['approved_stamp_filepath']);
            }

            // Generate new filename
            $approvalStampName = 'public/uploads/org_approval_stamps/' . $org['orgcode'] . '_' . time() . '.' . $approvalStamp->getExtension();

            // Move file to uploads directory
            if (!is_dir('public/uploads/org_approval_stamps')) {
                mkdir('public/uploads/org_approval_stamps', 0777, true);
            }
            $approvalStamp->move(ROOTPATH . 'public/uploads/org_approval_stamps', basename($approvalStampName));
        }

        // Prepare data for update
        $data = [
            'orgcode' => $this->request->getPost('orgcode'),
            'name' => $this->request->getPost('name'),
            'description' => $this->request->getPost('description'),
            'addlockprov' => $this->request->getPost('addlockprov'),
            'addlockcountry' => $this->request->getPost('addlockcountry'),
            'postal_address' => $this->request->getPost('postal_address'),
            'phones' => $this->request->getPost('phones'),
            'emails' => $this->request->getPost('emails'),
            'province_json' => $this->request->getPost('province_json'),
            'district_json' => $this->request->getPost('district_json'),
            'llg_json' => $this->request->getPost('llg_json'),
            'is_locationlocked' => $this->request->getPost('is_locationlocked') ? 1 : 0,
            'is_active' => $this->request->getPost('is_active') ? 1 : 0,
            'license_status' => $this->request->getPost('license_status'),
            'signature_name' => $this->request->getPost('signature_name'),
            'signature_position' => $this->request->getPost('signature_position'),
            'updated_by' => $this->session->get('user_id'),
            'orglogo' => $logoName,
            'signature_filepath' => $signatureName,
            'stamp_filepath' => $stampName,
            'approved_stamp_filepath' => $approvalStampName
        ];

        // Only update files if new ones were uploaded
        if ($signature->isValid()) {
            $data['signature_filepath'] = $signatureName;
        }
        if ($stamp->isValid()) {
            $data['stamp_filepath'] = $stampName;
        }

        // Validate emails
        $emails = explode(',', $data['emails']);
        foreach ($emails as $email) {
            $email = trim($email);
            if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return redirect()->back()
                    ->with('error', 'Invalid email format: ' . $email)
                    ->withInput();
            }
        }

        try {
            // Update the organization record
            if ($this->orgModel->update($org['id'], $data)) {
                $this->session->setFlashdata('success', 'Organization settings updated successfully.');
                return redirect()->back();
            }

            return redirect()->back()
                ->with('error', 'Failed to update organization settings. ' . implode(', ', $this->orgModel->errors()))
                ->withInput();
        } catch (\Exception $e) {
            log_message('error', '[OrgSettings::update] Error updating organization: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while updating the organization settings.')
                ->withInput();
        }
    }
} 
