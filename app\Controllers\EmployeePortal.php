<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\employeesModel;
use App\Models\PositionsModel;
use App\Models\GroupingsModel;

class EmployeePortal extends BaseController
{
    protected $employeesModel;
    protected $positionsModel;
    protected $groupingsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'text']);
        $this->employeesModel = new employeesModel();
        $this->positionsModel = new PositionsModel();
        $this->groupingsModel = new GroupingsModel();
        $this->session = session();
    }

    public function search()
    {
        $data = [
            'title' => 'Employee Login',
        ];

        $fileno = $this->request->getPost('fileno');

        $employee = $this->employeesModel->getEmployeeWithDetails('fileno', $fileno);

        if (!$employee) {
            return redirect()->to('/')->with('error', 'Employee not found');
        }

        $data['employee'] = $employee;
        return view('employee_portal/login', $data);
    }

    public function login()
    {
        $emp_id = $this->request->getPost('emp_id');
        $password = $this->request->getPost('password');

        // Get employee with full details including position
        $employee = $this->employeesModel->getEmployeeWithDetails('emp_id', $emp_id);

        if (!$employee) {
            return redirect()->to('/')->with('error', 'Employee not found');
        }

        // For debugging - log the input values (remove in production)
        log_message('debug', 'Login attempt - File No: ' . $employee['fileno'] . ', Password: ' . $password);

        // Try both the file number and the hashed password
        if ($password === $employee['fileno'] || password_verify($password, $employee['password'])) {
            // If login successful with file number, update the password hash
           /*  if ($password === $employee['fileno']) {
                $this->employeesModel->update($emp_id, [
                    'password' => password_hash($password, PASSWORD_DEFAULT)
                ]);
            } */

            $this->session->set([
                'emp_id' => $employee['emp_id'],
                'name' => $employee['fname'] . ' ' . $employee['lname'],
                'position' => $employee['position_name'],
                'appointment_type' => $employee['appointment_type'],
                'group' => $employee['group_name'],
                'isEmployeeLoggedIn' => true
            ]);
            return redirect()->to('employee_portal/dashboard');
        }

        // Get file number for error message
        $fileno = $employee['fileno'];

        // Redirect back to login page with error message
        return redirect()->to("/")
            ->withInput()
            ->with('error', 'Invalid password. Please use your file number (' . $fileno . ') as your password.');
    }

    public function dashboard()
    {
        if (!$this->session->get('isEmployeeLoggedIn')) {
            return redirect()->to('/');
        }

        $data = [
            'title' => 'Employee Dashboard',
        ];

        $emp_id = $this->session->get('emp_id');

        // Get employee details with position and group info
        $employee = $this->employeesModel->getEmployeeWithDetails('emp_id', $emp_id);

        if (!$employee) {
            $this->session->destroy();
            return redirect()->to('/')->with('error', 'Employee not found');
        }

        $data['employee'] = $employee;
        return view('employee_portal/dashboard', $data);
    }

    public function logout()
    {
        $this->session->destroy();
        return redirect()->to('/')->with('success', 'Successfully logged out');
    }
}