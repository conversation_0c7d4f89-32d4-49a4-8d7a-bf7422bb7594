<?php namespace App\Models;

use CodeIgniter\Model;

class PayslipsModel extends Model
{
    protected $table = 'payslips';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $allowedFields = [
        'org_id',
        'emp_id',
        'pay_no',
        'pay_date',
        'month',
        'year',
        'file_path',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation rules
    protected $validationRules = [];

    public function __construct()
    {
        parent::__construct();
        $this->orderBy('pay_date', 'DESC');
    }

    // Get payslips by date range
    public function getPayslipsByDateRange($startDate, $endDate, $org_id) 
    {
        $query = $this->where('org_id', $org_id)
                    ->where('pay_date >=', $startDate)
                    ->where('pay_date <=', $endDate)
                    ->orderBy('pay_date', 'DESC');
        
        // Log the query for debugging
        log_message('debug', 'Payslips Query: ' . $this->db->getLastQuery());
        
        $result = $query->findAll();
        log_message('debug', 'Payslips Result Count: ' . count($result));
        
        return $result;
    }
    
    /**
     * Get all payslips for dropdown selection
     * 
     * @param int $org_id Organization ID
     * @return array Array of payslips with id, pay_no, and pay_date
     */
    public function getAllPayslipsForDropdown($org_id)
    {
        return $this->select('id, pay_no, pay_date')
                    ->where('org_id', $org_id)
                    ->orderBy('pay_date', 'DESC')
                    ->findAll();
    }
    
    /**
     * Get a single payslip by ID
     * 
     * @param int $id Payslip ID
     * @return array|null Payslip data or null if not found
     */
    public function getPayslipById($id)
    {
        return $this->find($id);
    }
}
