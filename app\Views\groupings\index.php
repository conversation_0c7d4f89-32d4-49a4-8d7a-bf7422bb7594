<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <script>
        // Display success message if exists
        <?php if(session()->has('success')): ?>
            Swal.fire({
                icon: 'success',
                title: '<?= session()->getFlashdata('success') ?>',
                showConfirmButton: false,
                timer: 2000,
                position: 'top-end',
                toast: true
            });
        <?php endif; ?>

        // Display error message if exists
        <?php if(session()->has('error')): ?>
            Swal.fire({
                icon: 'error',
                title: '<?= session()->getFlashdata('error') ?>',
                showConfirmButton: false,
                timer: 2000,
                position: 'top-end',
                toast: true
            });
        <?php endif; ?>
    </script>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Groups Management</h5>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
                <i class="fas fa-plus"></i> Add New Group
            </button>
        </div>
        <div class="card-body">
            <table class="table table-striped" id="groupsTable">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Parent Group</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($groups as $group): ?>
                    <tr>
                        <td><?= esc($group['name']) ?></td>
                        <td><?= esc($group['description']) ?></td>
                        <td><?= $group['parent_id'] ? esc($parents[$group['parent_id']] ?? 'None') : 'None' ?></td>
                        <td>
                            <div class="d-flex">
                                <a href="<?= base_url('positions/index/' . $group['id']) ?>" class="btn btn-sm btn-info me-2">
                                    <i class="fas fa-tasks"></i> Manage Positions
                                </a>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#editModal<?= $group['id'] ?>">
                                                <i class="fas fa-edit me-2"></i> Edit
                                            </button>
                                        </li>
                                        <li>
                                            <a href="<?= base_url('groups/delete/' . $group['id']) ?>" 
                                               class="dropdown-item text-danger"
                                               onclick="return confirm('Are you sure you want to delete <?= esc($group['name']) ?>?')">
                                                <i class="fas fa-trash me-2"></i> Delete
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>

                    <!-- Edit Modal for <?= esc($group['name']) ?> -->
                    <div class="modal fade" id="editModal<?= $group['id'] ?>" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Edit Group: <?= esc($group['name']) ?></h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <form action="<?= base_url('groups/update/' . $group['id']) ?>" method="post">
                                    <?= csrf_field() ?>
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label class="form-label">Name</label>
                                            <input type="text" class="form-control" name="name" value="<?= esc($group['name']) ?>" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" name="description" required><?= esc($group['description']) ?></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Parent Group</label>
                                            <select class="form-select" name="parent_id">
                                                <option value="">None</option>
                                                <?php foreach ($parents as $id => $name): ?>
                                                    <?php if ($id != $group['id']): // Prevent self-reference ?>
                                                    <option value="<?= $id ?>" <?= $group['parent_id'] == $id ? 'selected' : '' ?>><?= esc($name) ?></option>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        <button type="submit" class="btn btn-primary">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Organizational Chart Section -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">Organizational Structure</h5>
    </div>
    <div class="card-body">
        <div id="chart_wrapper">
            <div id="chart_div"></div>
        </div>
    </div>
</div>

<!-- Add Modal -->
<div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('groups/store') ?>" method="post">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Parent Group</label>
                        <select class="form-select" name="parent_id">
                            <option value="">None</option>
                            <?php foreach ($parents as $id => $name): ?>
                            <option value="<?= $id ?>"><?= esc($name) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>


<style>
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap');

/* The outer wrapper that will be used for scaling */
#chart_wrapper {
    position: relative;
    width: 100%;
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding: 20px;
}

/* The chart container holds the org chart */
#chart_div {
    position: absolute;
    transform-origin: top left;
    transition: transform 0.3s ease-in-out;
}

.org-card {
    width: 180px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(206, 17, 38, 0.15);
    background: #ffffff;
    margin: 0 auto;
    transition: all 0.3s;
}

.org-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(206, 17, 38, 0.2);
}

.org-card-header {
    background: linear-gradient(135deg, #CE1126, #8B0C1A);
    color: #FCD116;
    padding: 10px;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
}

.org-card-body {
    padding: 10px;
    text-align: center;
    color: #000000;
    font-size: 14px;
    border-top: 2px solid #FCD116;
}

/* Style the chart lines */
#chart_div .google-visualization-orgchart-lineleft {
    border-left: 2px solid #CE1126 !important;
}

#chart_div .google-visualization-orgchart-lineright {
    border-right: 2px solid #CE1126 !important;
}

#chart_div .google-visualization-orgchart-linebottom {
    border-bottom: 2px solid #CE1126 !important;
}

/* Add hover effect to nodes */
#chart_div div[style*="position: absolute"] {
    transition: all 0.3s ease;
}

#chart_div div[style*="position: absolute"]:hover {
    transform: scale(1.05);
}

@media (max-width: 768px) {
    #chart_wrapper {
        min-height: 300px;
    }
}
</style>

<!-- Add jQuery first -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- Then Google Charts -->
<script>
(function() {
    var chartLoaded = false;
    var maxRetries = 3;
    var retryCount = 0;
    var retryInterval = 1000; // 1 second
    var BASE_CHART_WIDTH = 1000;
    var chart;
    var data;

    function loadChartApi() {
        return new Promise((resolve, reject) => {
            try {
                // Check if Google Charts is already loaded
                if (typeof google !== 'undefined' && google.visualization) {
                    chartLoaded = true;
                    resolve();
                    return;
                }

                // Load the script manually if needed
                var script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = 'https://www.gstatic.com/charts/loader.js';
                script.async = true;

                script.onload = function() {
                    google.charts.load('current', { 
                        packages: ['orgchart'],
                        callback: function() {
                            chartLoaded = true;
                            resolve();
                        }
                    });
                };

                script.onerror = function() {
                    reject(new Error('Failed to load Google Charts'));
                };

                document.head.appendChild(script);
            } catch (e) {
                reject(e);
            }
        });
    }

    function retryLoadChart() {
        if (retryCount < maxRetries) {
            retryCount++;
            setTimeout(function() {
                initializeChart();
            }, retryInterval * retryCount);
        } else {
            // If all retries fail, show fallback view
            showFallbackView();
        }
    }

    function showFallbackView() {
        var chartDiv = document.getElementById('chart_div');
        if (chartDiv) {
            var groups = <?= json_encode($groups) ?>;
            var html = '<div class="fallback-chart">';
            
            // Create a simple tree view as fallback
            function renderGroup(group, level = 0) {
                var padding = level * 20;
                html += `
                    <div class="fallback-node" style="padding-left: ${padding}px">
                        <div class="org-card">
                            <div class="org-card-header">${group.name}</div>
                            <div class="org-card-body">
                                <div class="text-muted small mb-1">Description:</div>
                                ${group.description}
                            </div>
                        </div>
                    </div>
                `;
                
                // Find and render children
                var children = groups.filter(g => g.parent_id === group.id);
                children.forEach(child => renderGroup(child, level + 1));
            }
            
            // Start with root nodes (no parent)
            var rootNodes = groups.filter(g => !g.parent_id);
            rootNodes.forEach(node => renderGroup(node));
            
            html += '</div>';
            chartDiv.innerHTML = html;
            
            // Add fallback styles
            var style = document.createElement('style');
            style.textContent = `
                .fallback-chart {
                    padding: 20px;
                }
                .fallback-node {
                    margin-bottom: 20px;
                    position: relative;
                }
                .fallback-node::before {
                    content: '';
                    position: absolute;
                    left: -10px;
                    top: 50%;
                    width: 20px;
                    height: 2px;
                    background: #CE1126;
                }
                .fallback-node:not(:first-child)::after {
                    content: '';
                    position: absolute;
                    left: -10px;
                    top: -10px;
                    width: 2px;
                    height: calc(100% + 20px);
                    background: #CE1126;
                }
            `;
            document.head.appendChild(style);
        }
    }

    function initializeChart() {
        loadChartApi()
            .then(() => {
                initChart();
            })
            .catch((error) => {
                console.error('Error loading chart:', error);
                retryLoadChart();
            });
    }

    function initChart() {
        try {
            if (!chartLoaded) {
                throw new Error('Google Charts not loaded');
            }

            data = new google.visualization.DataTable();
            data.addColumn('string', 'Name');
            data.addColumn('string', 'Manager');
            data.addColumn('string', 'ToolTip');

            // Get the PHP groups data
            var groups = <?= json_encode($groups) ?>;
            
            // Convert PHP groups data to chart format
            groups.forEach(function(group) {
                var parentId = group.parent_id ? group.parent_id.toString() : '';
                var nodeHTML = `
                    <div class="org-card">
                        <div class="org-card-header">${group.name}</div>
                        <div class="org-card-body">
                            <div class="text-muted small mb-1">Description:</div>
                            ${group.description}
                        </div>
                    </div>
                `;
                data.addRow([
                    { v: group.id.toString(), f: nodeHTML },
                    parentId,
                    group.name
                ]);
            });

            chart = new google.visualization.OrgChart(document.getElementById('chart_div'));
            drawChart();

            // Handle window resize
            window.addEventListener('resize', debounce(function() {
                if (chart) {
                    drawChart();
                }
            }, 250));

        } catch (e) {
            console.error('Error initializing chart:', e);
            document.getElementById('chart_div').innerHTML = '<div class="alert alert-warning">Unable to display organizational chart. Please try refreshing the page.</div>';
        }
    }

    function drawChart() {
        try {
            var options = {
                allowHtml: true,
                size: 'medium',
                nodeClass: 'org-node',
                selectedNodeClass: 'org-node-selected',
                allowCollapse: true
            };
            
            chart.draw(data, options);
            
            // Add a small delay before scaling to ensure chart is fully rendered
            setTimeout(adjustChartScale, 100);
        } catch (e) {
            console.error('Error drawing chart:', e);
        }
    }

    function adjustChartScale() {
        var wrapper = document.getElementById('chart_wrapper');
        var chartDiv = document.getElementById('chart_div');
        
        if (!wrapper || !chartDiv) return;

        // Get wrapper dimensions
        var containerWidth = wrapper.clientWidth - 40; // Account for padding
        var containerHeight = Math.max(400, window.innerHeight * 0.6); // Min height or 60% of viewport

        // Get actual chart dimensions
        var unscaledWidth = chartDiv.scrollWidth || BASE_CHART_WIDTH;
        var unscaledHeight = chartDiv.scrollHeight || containerHeight;

        // Compute the scale factor (smallest of width/height ratios)
        var scaleX = containerWidth / unscaledWidth;
        var scaleY = containerHeight / unscaledHeight;
        var scale = Math.min(scaleX, scaleY, 1); // Ensure we don't exceed original size

        // Apply scale transformation
        chartDiv.style.transform = `scale(${scale})`;
        chartDiv.style.transformOrigin = "top left";

        // Center the chart
        var scaledWidth = unscaledWidth * scale;
        var scaledHeight = unscaledHeight * scale;
        
        chartDiv.style.left = `${Math.max(0, (containerWidth - scaledWidth) / 2)}px`;
        chartDiv.style.top = `${Math.max(0, (containerHeight - scaledHeight) / 2)}px`;

        // Adjust wrapper height dynamically
        wrapper.style.height = `${Math.max(scaledHeight + 40, 400)}px`; // Minimum height of 400px
    }

    // Debounce function to limit how often we adjust on resize
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this,
                  args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }

    // Start the initialization process
    initializeChart();

    // Reset forms when modals are hidden
    $('.modal').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
    });
})();

$(document).ready(function() {
    $('#groupsTable').DataTable();
});
</script>
<?= $this->endSection() ?>
