<?php

namespace App\Controllers;

use CodeIgniter\HTTP\ResponseInterface;
use App\Models\PayslipsModel;
use Smalot\PdfParser\Parser;

/**
 * SalariesController
 * 
 * Controller for handling salary-related operations and reports
 */
class SalariesController extends BaseController
{
    /**
     * PayslipsModel instance
     * 
     * @var PayslipsModel
     */
    protected $payslipsModel;
    
    /**
     * Constructor method
     */
    public function __construct()
    {
        // Load helpers
        helper(['form', 'url', 'info']);
        
        // Initialize model
        $this->payslipsModel = new PayslipsModel();
    }

    /**
     * Index method - redirects to salary_analysis
     * 
     * @return ResponseInterface
     */
    public function index()
    {
        return redirect()->to(site_url('salaries/salary_analysis'));
    }

    /**
     * Salary Analysis page
     * 
     * @return string|ResponseInterface View or redirect response
     */
    public function salary_analysis()
    {
        // Check if user is logged in
        if (!session()->get('logged_in')) {
            return redirect()->to(site_url('login'));
        }
        
        $org_id = session()->get('org_id');
        
        // Get all payslips for dropdown
        $payslips = $this->payslipsModel->getAllPayslipsForDropdown($org_id);
        
        $data = [
            'title' => 'Salary Analysis',
            'menu' => 'salary',
            'payslips' => $payslips
        ];
        
        // Process form submission if any
        if ($this->request->getMethod() === 'post') {
            $startPayslipId = $this->request->getPost('start_payslip');
            $endPayslipId = $this->request->getPost('end_payslip');
            
            if ($startPayslipId && $endPayslipId) {
                // Get payslip details
                $startPayslip = $this->payslipsModel->getPayslipById($startPayslipId);
                $endPayslip = $this->payslipsModel->getPayslipById($endPayslipId);
                
                if ($startPayslip && $endPayslip) {
                    $data['start_payslip'] = $startPayslip;
                    $data['end_payslip'] = $endPayslip;
                    $data['selected_start'] = $startPayslipId;
                    $data['selected_end'] = $endPayslipId;
                    $data['analysis'] = $this->analyzePayslips($startPayslip['pay_date'], $endPayslip['pay_date']);
                }
            }
        } else {
            // Default: analyze the latest payslip
            $latestPayslip = $this->getLatestPayslip($org_id);
            if ($latestPayslip) {
                $data['start_payslip'] = $latestPayslip;
                $data['end_payslip'] = $latestPayslip;
                $data['selected_start'] = $latestPayslip['id'];
                $data['selected_end'] = $latestPayslip['id'];
                $data['analysis'] = $this->analyzePayslips($latestPayslip['pay_date'], $latestPayslip['pay_date']);
            }
        }
        
        return view('salaries/salary_analysis', $data);
    }
    
    /**
     * Get the latest payslip for an organization
     * 
     * @param int $org_id Organization ID
     * @return array|null Latest payslip or null if none found
     */
    private function getLatestPayslip(int $org_id)
    {
        return $this->payslipsModel->where('org_id', $org_id)
                                  ->orderBy('pay_date', 'DESC')
                                  ->first();
    }
    
    /**
     * Analyze payslips within a date range
     * 
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @return array Analysis results
     */
    private function analyzePayslips(string $startDate, string $endDate): array
    {
        $org_id = session()->get('org_id');
        $payslips = $this->payslipsModel->getPayslipsByDateRange($startDate, $endDate, $org_id);
        
        if (empty($payslips)) {
            return [
                'success' => false,
                'message' => 'No payslips found for the selected date range.'
            ];
        }
        
        // Initialize analysis data structure
        $analysis = [
            'success' => true,
            'payslip_count' => count($payslips),
            'total_base_salary' => 0,
            'total_overtime' => 0,
            'total_gross' => 0,
            'total_tax' => 0,
            'total_deductions' => 0,
            'total_super' => 0,
            'total_net' => 0,
            'monthly_data' => [],
            'payslips' => []
        ];
        
        // Process each payslip
        foreach ($payslips as $payslip) {
            $payslipData = $this->extractPayslipData($payslip);
            
            if ($payslipData['success']) {
                // Add to totals
                $analysis['total_base_salary'] += $payslipData['base_salary'];
                $analysis['total_overtime'] += $payslipData['overtime'];
                $analysis['total_gross'] += $payslipData['gross'];
                $analysis['total_tax'] += $payslipData['tax'];
                $analysis['total_deductions'] += $payslipData['deductions'];
                $analysis['total_super'] += $payslipData['super'];
                $analysis['total_net'] += $payslipData['net'];
                
                // Add to monthly data for charts
                $month = date('Y-m', strtotime($payslip['pay_date']));
                if (!isset($analysis['monthly_data'][$month])) {
                    $analysis['monthly_data'][$month] = [
                        'base_salary' => 0,
                        'overtime' => 0,
                        'gross' => 0,
                        'tax' => 0,
                        'deductions' => 0,
                        'super' => 0,
                        'net' => 0
                    ];
                }
                
                $analysis['monthly_data'][$month]['base_salary'] += $payslipData['base_salary'];
                $analysis['monthly_data'][$month]['overtime'] += $payslipData['overtime'];
                $analysis['monthly_data'][$month]['gross'] += $payslipData['gross'];
                $analysis['monthly_data'][$month]['tax'] += $payslipData['tax'];
                $analysis['monthly_data'][$month]['deductions'] += $payslipData['deductions'];
                $analysis['monthly_data'][$month]['super'] += $payslipData['super'];
                $analysis['monthly_data'][$month]['net'] += $payslipData['net'];
                
                // Add payslip details
                $analysis['payslips'][] = [
                    'id' => $payslip['id'],
                    'pay_no' => $payslip['pay_no'],
                    'pay_date' => $payslip['pay_date'],
                    'data' => $payslipData
                ];
            }
        }
        
        // Sort monthly data by date
        ksort($analysis['monthly_data']);
        
        // Convert monthly data to arrays for charts
        $chartData = [
            'labels' => [],
            'base_salary' => [],
            'overtime' => [],
            'gross' => [],
            'tax' => [],
            'deductions' => [],
            'super' => [],
            'net' => []
        ];
        
        foreach ($analysis['monthly_data'] as $month => $data) {
            $chartData['labels'][] = date('M Y', strtotime($month . '-01'));
            $chartData['base_salary'][] = $data['base_salary'];
            $chartData['overtime'][] = $data['overtime'];
            $chartData['gross'][] = $data['gross'];
            $chartData['tax'][] = $data['tax'];
            $chartData['deductions'][] = $data['deductions'];
            $chartData['super'][] = $data['super'];
            $chartData['net'][] = $data['net'];
        }
        
        $analysis['chart_data'] = $chartData;
        
        return $analysis;
    }
    
    /**
     * Extract data from a payslip PDF
     * 
     * @param array $payslip Payslip record from database
     * @return array Extracted data
     */
    private function extractPayslipData(array $payslip): array
    {
        $filePath = FCPATH . $payslip['file_path'];
        
        if (!file_exists($filePath)) {
            return [
                'success' => false,
                'message' => 'Payslip file not found: ' . $filePath
            ];
        }
        
        try {
            $parser = new Parser();
            $pdf = $parser->parseFile($filePath);
            $pages = $pdf->getPages();
            
            // Initialize data
            $data = [
                'success' => true,
                'base_salary' => 0,
                'overtime' => 0,
                'gross' => 0,
                'tax' => 0,
                'deductions' => 0,
                'super' => 0,
                'net' => 0,
                'pages_data' => [], // Store data for each page/employee
                'employee_count' => 0 // Initialize employee count
            ];
            
            // Process each page (each page is a payslip for one employee)
            foreach ($pages as $pageIndex => $page) {
                $text = $page->getText();
                $lines = explode("\n", $text);
                
                // Initialize page data
                $pageData = [
                    'base_salary' => 0,
                    'overtime' => 0,
                    'gross' => 0,
                    'tax' => 0,
                    'deductions' => 0,
                    'super' => 0,
                    'net' => 0,
                    'has_employee' => false // Flag to track if this page contains an employee
                ];
                
                $foundBaseSalary = false;
                $foundSummary = false;
                $foundNet = false;
                $foundDeduction = false;
                $baseSalaryLineIndex = 0;
                $summaryLineIndex = 0;
                $netLineIndex = 0;
                $deductionLineIndex = 0;
                
                // Check if this page contains the word "Employee"
                if (stripos($text, 'Employee') !== false) {
                    $pageData['has_employee'] = true;
                    $data['employee_count']++; // Increment the employee count
                }
                
                // Find the line indexes for Base Salary, Summary, and Net
                foreach ($lines as $lineIndex => $line) {
                    if (strpos($line, 'Base Salary') !== false) {
                        $foundBaseSalary = true;
                        $baseSalaryLineIndex = $lineIndex;
                    }
                    
                    if (strpos($line, 'Summary') !== false) {
                        $foundSummary = true;
                        $summaryLineIndex = $lineIndex;
                    }
                    
                    if (strpos($line, 'Deduction') !== false && strpos($line, 'Deductions') === false) {
                        $foundDeduction = true;
                        $deductionLineIndex = $lineIndex;
                    }
                    
                    if (strpos($line, 'Net') !== false) {
                        $foundNet = true;
                        $netLineIndex = $lineIndex;
                    }
                }
                
                // Extract Base Salary (line 5 after Base Salary keyword)
                if ($foundBaseSalary && isset($lines[$baseSalaryLineIndex + 4])) {
                    $pageData['base_salary'] = $this->extractAmount($lines[$baseSalaryLineIndex + 4]);
                }
                
                // Extract data after Summary keyword
                if ($foundSummary) {
                    // Overtime (line 9 after Summary)
                    if (isset($lines[$summaryLineIndex + 9])) {
                        $pageData['overtime'] = $this->extractAmount($lines[$summaryLineIndex + 9]);
                    }
                    
                    // Gross (line 10 after Summary)
                    if (isset($lines[$summaryLineIndex + 10])) {
                        $pageData['gross'] = $this->extractAmount($lines[$summaryLineIndex + 10]);
                    }
                    
                    // Tax (line 11 after Summary)
                    if (isset($lines[$summaryLineIndex + 11])) {
                        $pageData['tax'] = $this->extractAmount($lines[$summaryLineIndex + 11]);
                    }
                    
                    // Deductions (line 5 after Deduction keyword)
                    if (isset($lines[$deductionLineIndex + 5])) {
                        $pageData['deductions'] = $this->extractAmount($lines[$deductionLineIndex + 5]);
                    }
                    
                    // Super (line 20 after Summary)
                    if (isset($lines[$summaryLineIndex + 20])) {
                        $pageData['super'] = $this->extractAmount($lines[$summaryLineIndex + 20]);
                    }
                }
                
                // Extract Net (line 14 after Net keyword)
                if ($foundNet && isset($lines[$netLineIndex + 13])) {
                    $pageData['net'] = $this->extractAmount($lines[$netLineIndex + 13]);
                }
                
                // Log the extracted data for debugging
                log_message('debug', 'Page ' . ($pageIndex + 1) . ' data: ' . json_encode($pageData));
                
                // Add to the total
                $data['base_salary'] += $pageData['base_salary'];
                $data['overtime'] += $pageData['overtime'];
                $data['gross'] += $pageData['gross'];
                $data['tax'] += $pageData['tax'];
                $data['deductions'] += $pageData['deductions'];
                $data['super'] += $pageData['super'];
                $data['net'] += $pageData['net'];
                
                // Store the page data
                $data['pages_data'][] = $pageData;
            }
            
            // Log the total data for debugging
            log_message('debug', 'Total payslip data: ' . json_encode($data));
            
            return $data;
        } catch (\Exception $e) {
            log_message('error', 'Error parsing PDF: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error parsing PDF: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Extract numeric amount from a string
     * 
     * @param string $text Text containing an amount
     * @return float Extracted amount
     */
    private function extractAmount(string $text): float
    {
        // Remove non-numeric characters except for decimal point
        $amount = preg_replace('/[^0-9.-]/', '', $text);
        return floatval($amount);
    }
    
    /**
     * AJAX endpoint to get salary analysis data
     * 
     * @return ResponseInterface
     */
    public function getSalaryAnalysisData(): ResponseInterface
    {
        // Check if user is logged in
        if (!session()->get('logged_in')) {
            return $this->response->setJSON(['error' => 'Not logged in']);
        }
        
        $startPayslipId = $this->request->getPost('start_payslip');
        $endPayslipId = $this->request->getPost('end_payslip');
        
        if (!$startPayslipId || !$endPayslipId) {
            return $this->response->setJSON(['error' => 'Start payslip and end payslip are required']);
        }
        
        // Get payslip details
        $startPayslip = $this->payslipsModel->getPayslipById($startPayslipId);
        $endPayslip = $this->payslipsModel->getPayslipById($endPayslipId);
        
        if (!$startPayslip || !$endPayslip) {
            return $this->response->setJSON(['error' => 'Invalid payslip selection']);
        }
        
        $analysis = $this->analyzePayslips($startPayslip['pay_date'], $endPayslip['pay_date']);
        return $this->response->setJSON($analysis);
    }
}
