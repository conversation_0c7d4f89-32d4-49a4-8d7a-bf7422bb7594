<?= $this->extend('templates/employees_portal_temp') ?>

<?= $this->section('content') ?>
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">My Letters</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="<?= base_url('employee_portal/dashboard') ?>">Dashboard</a></li>
                                <li class="breadcrumb-item active">Letters</li>
                            </ol>
                        </nav>
                    </div>

                    <?php if(session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger" role="alert">
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <?php if(session()->getFlashdata('success')): ?>
                        <div class="alert alert-success" role="alert">
                            <?= session()->getFlashdata('success') ?>
                        </div>
                    <?php endif; ?>

                    <!-- Request Letters Section -->
                    <div class="row">
                        <!-- Confirmation Letter Request Card -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title mb-4">Request Confirmation Letter</h5>
                                    <form action="<?= base_url('employee_portal/my_letters/request_confirmation') ?>" method="POST">
                                        <?= csrf_field() ?>
                                        <div class="mb-3">
                                            <label for="confirmationAddress" class="form-label">Address To</label>
                                            <br><em><small class="form-text text-muted">Eg. BSP Bank <br> Wewak, <br> Papua New Guinea</small></em>
                                            <textarea class="form-control" id="confirmationAddress" name="address" rows="3" required 
                                                placeholder="Enter the address of the recipient"></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-2"></i>Request Confirmation Letter
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        

                        <!-- Other Letters Request Card -->
                        <!-- <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title mb-4">Request Other Letters</h5>
                                    <form action="<?= base_url('employee_portal/my_letters/request_other') ?>" method="POST">
                                        <?= csrf_field() ?>
                                        <div class="mb-3">
                                            <label for="otherAddress" class="form-label">Address To</label>
                                            <textarea class="form-control" id="otherAddress" name="address" rows="2" required 
                                                placeholder="Enter the address of the recipient"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="subject" class="form-label">Subject</label>
                                            <input type="text" class="form-control" id="subject" name="subject" required 
                                                placeholder="Enter the subject of the letter">
                                        </div>
                                        <div class="mb-3">
                                            <label for="purpose" class="form-label">Purpose</label>
                                            <textarea class="form-control" id="purpose" name="purpose" rows="2" required 
                                                placeholder="Explain the purpose of the letter"></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-2"></i>Request Other Letter
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div> -->
                    </div>

                    <!-- Letters History Table -->
                    <div class="table-responsive mt-4">
                        <h5 class="mb-3">Letter Request History</h5>
                        <table class="table table-hover" id="lettersTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Subject</th>
                                    <th>Address To</th>
                                    <th>Status</th>
                                    <th>Remarks</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (isset($letters) && !empty($letters)): ?>
                                    <?php foreach ($letters as $letter): ?>
                                        <tr>
                                            <td><?= date('d M Y', strtotime($letter['created_at'])) ?></td>
                                            <td><?= ucfirst(esc($letter['letter_type'])) ?></td>
                                            <td><?= esc($letter['subject']) ?></td>
                                            <td>
                                                <?php 
                                                    // Truncate address if too long
                                                    $address = esc($letter['address_to']);
                                                    echo (strlen($address) > 50) ? substr($address, 0, 50) . '...' : $address;
                                                ?>
                                                <?php if (strlen($address) > 50): ?>
                                                    <i class="fas fa-info-circle text-primary" 
                                                       data-bs-toggle="tooltip" 
                                                       title="<?= esc($letter['address_to']) ?>">
                                                    </i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $letter['confirmation_status'] == 'approved' ? 'success' : 
                                                    ($letter['confirmation_status'] == 'rejected' ? 'danger' : 'warning') ?>">
                                                    <?= ucfirst($letter['confirmation_status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($letter['confirmation_remarks'])): ?>
                                                    <button type="button" class="btn btn-sm btn-link p-0" 
                                                            data-bs-toggle="tooltip" 
                                                            title="<?= esc($letter['confirmation_remarks']) ?>">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($letter['confirmation_status'] == 'approved'): ?>
                                                    <a href="<?= base_url('employee_portal/my_letters/download/' . $letter['id']) ?>" 
                                                       class="btn btn-sm btn-primary" 
                                                       data-bs-toggle="tooltip" 
                                                       title="Download Letter">
                                                        <i class="fas fa-download"></i> Download Letter
                                                    </a>
                                                <?php elseif ($letter['confirmation_status'] == 'pending'): ?>
                                                    <span class="badge bg-warning text-dark">
                                                        <i class="fas fa-clock me-1"></i>Pending
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times me-1"></i>Rejected
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center">No letter requests found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#lettersTable').DataTable({
        order: [[0, 'desc']], // Sort by date descending
        pageLength: 10,
        responsive: true
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 