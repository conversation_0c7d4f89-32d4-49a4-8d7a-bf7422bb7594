<?php namespace App\Controllers;

use App\Models\GroupingsModel;
use App\Models\PositionsModel;
use CodeIgniter\HTTP\Response;

class Groups extends BaseController
{
    protected $groupingsModel;
    protected $positionsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'security']);
        $this->groupingsModel = new GroupingsModel();
        $this->positionsModel = new PositionsModel();
        $this->session = session();
    }

    public function index()
    {
        $org_id = $this->session->get('org_id');
        // Fetch all groups for the table
        $groups = $this->groupingsModel->where('org_id', $org_id)->orderBy('parent_id', 'ASC')->findAll();
        
        // Prepare data for org chart
        $chartData = [];
        foreach ($groups as $group) {
            $chartData[] = [
                'id' => $group['id'],
                'name' => esc($group['name']),
                'description' => esc($group['description']),
                'parent' => $group['parent_id'] ?: '' // Convert null to empty string
            ];
        }
        
        // Get groups for dropdown options
        $parents = [];
        foreach ($groups as $group) {
            $parents[$group['id']] = esc($group['name']);
        }
        
        $data = [
            'title' => 'Groups Management',
            'menu'  => 'positions',
            'groups' => $groups,
            'chartData' => $chartData,
            'parents' => $parents
        ];
        
        return view('groupings/index', $data);
    }

    public function store()
    {
        if (!$this->validate(['name' => 'required'])) {
            return redirect()->back()->with('error', 'Name is required');
        }

        try {
            $org_id = $this->session->get('org_id');
            $parent_id = $this->request->getPost('parent_id');

            // Validate parent group
            if ($parent_id) {
                $parentExists = $this->groupingsModel
                    ->where('org_id', $org_id)
                    ->where('id', $parent_id)
                    ->first();
                    
                if (!$parentExists) {
                    return redirect()->back()->with('error', 'Invalid parent group');
                }
            }

            $data = [
                'org_id' => $org_id,
                'name' => $this->request->getPost('name'),
                'description' => $this->request->getPost('description'),
                'parent_id' => $parent_id ?: null,
                'created_by' => $this->session->get('user_id')
            ];
            
            $this->groupingsModel->insert($data);
            return redirect()->to('groups')->with('success', 'Group added successfully');
        } catch (\Exception $e) {
            log_message('error', '[Groups::store] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while adding the group');
        }
    }

    public function update($id)
    {
        if (!$this->validate(['name' => 'required'])) {
            return redirect()->back()->with('error', 'Name is required');
        }

        try {
            $org_id = $this->session->get('org_id');
            $parent_id = $this->request->getPost('parent_id');

            // Check group existence
            $group = $this->groupingsModel->where('org_id', $org_id)->find($id);
            if (!$group) {
                return redirect()->back()->with('error', 'Group not found');
            }

            // Validate parent group
            if ($parent_id) {
                if ($parent_id == $id) {
                    return redirect()->back()->with('error', 'Group cannot be its own parent');
                }

                $parentExists = $this->groupingsModel
                    ->where('org_id', $org_id)
                    ->where('id', $parent_id)
                    ->first();
                    
                if (!$parentExists) {
                    return redirect()->back()->with('error', 'Invalid parent group');
                }
            }

            $data = [
                'name' => $this->request->getPost('name'),
                'description' => $this->request->getPost('description'),
                'parent_id' => $parent_id ?: null,
                'updated_by' => $this->session->get('user_id')
            ];
            
            $this->groupingsModel->update($id, $data);
            return redirect()->to('groups')->with('success', 'Group updated successfully');
        } catch (\Exception $e) {
            log_message('error', '[Groups::update] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while updating the group');
        }
    }

    public function delete($id)
    {
        try {
            $org_id = $this->session->get('org_id');

            // Check group existence
            $group = $this->groupingsModel->where('org_id', $org_id)->find($id);
            if (!$group) {
                return redirect()->back()->with('error', 'Group not found');
            }

            // Check for child groups
            $childCount = $this->groupingsModel->where('parent_id', $id)->countAllResults();
            if ($childCount > 0) {
                return redirect()->back()->with('error', 'Cannot delete group with child groups');
            }

            // Check for positions in this group
            $positionCount = $this->positionsModel->where('group_id', $id)->countAllResults();
            if ($positionCount > 0) {
                return redirect()->back()->with('error', 'Cannot delete group that has positions assigned to it');
            }

            $this->groupingsModel->delete($id);
            return redirect()->to('groups')->with('success', 'Group deleted successfully');
        } catch (\Exception $e) {
            log_message('error', '[Groups::delete] ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while deleting the group');
        }
    }
}
