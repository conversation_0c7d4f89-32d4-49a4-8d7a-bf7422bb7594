<?php

namespace App\Controllers;

use App\Models\EmployeeLeavesModel;
use App\Models\employeesModel;
use App\Models\PositionsModel;
use App\Models\GroupingsModel;

class Leave extends BaseController
{
    protected $employeeLeavesModel;
    protected $employeesModel;
    protected $positionsModel;
    protected $groupingsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'session']);
        $this->session = session();
        $this->employeeLeavesModel = new EmployeeLeavesModel();
        $this->employeesModel = new employeesModel();
        $this->positionsModel = new PositionsModel();
        $this->groupingsModel = new GroupingsModel();
    }

    /**
     * Display list of employees for leave management
     */
    public function index()
    {
        // Check if user is logged in
        if (!$this->session->get('logged_in')) {
            return redirect()->to('/login');
        }

        $org_id = $this->session->get('org_id');
        
        // Get employees with their position details
        $employees = $this->employeesModel
            ->select('employees.emp_id, employees.fileno, employees.fname, employees.lname, employees.status, positions.designation, groupings.name as group_name')
            ->join('positions', 'positions.employee_id = employees.emp_id', 'left')
            ->join('groupings', 'groupings.id = positions.group_id', 'left')
            ->where('employees.org_id', $org_id)
            ->where('employees.status', 'active')
            ->findAll();

        $data = [
            'title' => 'Manage Leave',
            'menu' => 'leave',
            'employees' => $employees
        ];

        return view('leave/leave_index', $data);
    }

    /**
     * Display leave records for a specific employee
     */
    public function employee($employee_id)
    {
        // Check if user is logged in
        if (!$this->session->get('logged_in')) {
            return redirect()->to('/login');
        }

        $org_id = $this->session->get('org_id');
        
        // Get employee details
        $employee = $this->employeesModel->getEmployeeWithDetails('emp_id', $employee_id);
        
        if (!$employee || $employee['org_id'] != $org_id) {
            $this->session->setFlashdata('error', 'Employee not found');
            return redirect()->to('/leave');
        }

        // Get employee leave records
        $leaves = $this->employeeLeavesModel->getLeavesByEmployee($employee_id);

        $data = [
            'title' => 'Employee Leave Management - ' . $employee['fname'] . ' ' . $employee['lname'],
            'menu' => 'leave',
            'employee' => $employee,
            'leaves' => $leaves
        ];

        return view('leave/leave_employee', $data);
    }

    /**
     * Show create leave form
     */
    public function create($employee_id)
    {
        // Check if user is logged in
        if (!$this->session->get('logged_in')) {
            return redirect()->to('/login');
        }

        $org_id = $this->session->get('org_id');
        
        // Get employee details
        $employee = $this->employeesModel->getEmployeeWithDetails('emp_id', $employee_id);
        
        if (!$employee || $employee['org_id'] != $org_id) {
            $this->session->setFlashdata('error', 'Employee not found');
            return redirect()->to('/leave');
        }

        $data = [
            'title' => 'Add Leave - ' . $employee['fname'] . ' ' . $employee['lname'],
            'menu' => 'leave',
            'employee' => $employee
        ];

        return view('leave/leave_create', $data);
    }

    /**
     * Store new leave record
     */
    public function store()
    {
        // Check if user is logged in
        if (!$this->session->get('logged_in')) {
            return redirect()->to('/login');
        }

        $org_id = $this->session->get('org_id');
        $user_id = $this->session->get('id');

        $data = [
            'org_id' => $org_id,
            'employee_id' => $this->request->getPost('employee_id'),
            'leave_type' => $this->request->getPost('leave_type'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => $user_id
        ];

        $result = $this->employeeLeavesModel->createLeave($data);

        if ($result['status']) {
            $this->session->setFlashdata('success', $result['message']);
        } else {
            $this->session->setFlashdata('error', $result['message']);
        }

        return redirect()->to('/leave/employee/' . $data['employee_id']);
    }

    /**
     * Show edit leave form
     */
    public function edit($leave_id)
    {
        // Check if user is logged in
        if (!$this->session->get('logged_in')) {
            return redirect()->to('/login');
        }

        $org_id = $this->session->get('org_id');
        
        // Get leave record
        $leave = $this->employeeLeavesModel->where('id', $leave_id)
                                          ->where('org_id', $org_id)
                                          ->where('is_deleted', false)
                                          ->first();
        
        if (!$leave) {
            $this->session->setFlashdata('error', 'Leave record not found');
            return redirect()->to('/leave');
        }

        // Get employee details
        $employee = $this->employeesModel->getEmployeeWithDetails('emp_id', $leave['employee_id']);

        $data = [
            'title' => 'Edit Leave - ' . $employee['fname'] . ' ' . $employee['lname'],
            'menu' => 'leave',
            'employee' => $employee,
            'leave' => $leave
        ];

        return view('leave/leave_edit', $data);
    }

    /**
     * Update leave record
     */
    public function update($leave_id)
    {
        // Check if user is logged in
        if (!$this->session->get('logged_in')) {
            return redirect()->to('/login');
        }

        $org_id = $this->session->get('org_id');
        $user_id = $this->session->get('id');

        // Verify leave belongs to organization
        $leave = $this->employeeLeavesModel->where('id', $leave_id)
                                          ->where('org_id', $org_id)
                                          ->where('is_deleted', false)
                                          ->first();
        
        if (!$leave) {
            $this->session->setFlashdata('error', 'Leave record not found');
            return redirect()->to('/leave');
        }

        $data = [
            'leave_type' => $this->request->getPost('leave_type'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => $user_id
        ];

        $result = $this->employeeLeavesModel->updateLeave($leave_id, $data);

        if ($result['status']) {
            $this->session->setFlashdata('success', $result['message']);
        } else {
            $this->session->setFlashdata('error', $result['message']);
        }

        return redirect()->to('/leave/employee/' . $leave['employee_id']);
    }

    /**
     * Delete leave record (soft delete)
     */
    public function delete($leave_id)
    {
        // Check if user is logged in
        if (!$this->session->get('logged_in')) {
            return redirect()->to('/login');
        }

        $org_id = $this->session->get('org_id');
        $user_id = $this->session->get('id');

        // Verify leave belongs to organization
        $leave = $this->employeeLeavesModel->where('id', $leave_id)
                                          ->where('org_id', $org_id)
                                          ->where('is_deleted', false)
                                          ->first();
        
        if (!$leave) {
            $this->session->setFlashdata('error', 'Leave record not found');
            return redirect()->to('/leave');
        }

        $result = $this->employeeLeavesModel->softDelete($leave_id, $user_id);

        if ($result) {
            $this->session->setFlashdata('success', 'Leave record deleted successfully');
        } else {
            $this->session->setFlashdata('error', 'Failed to delete leave record');
        }

        return redirect()->to('/leave/employee/' . $leave['employee_id']);
    }
}
