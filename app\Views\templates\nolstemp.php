<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Description" content="Government Public Servants Support System" />
    <!-- Add CSRF Token -->
    <meta name="<?= csrf_token() ?>" content="<?= csrf_hash() ?>" />
    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#CE1126">
    <link rel="manifest" href="<?= base_url() ?>public/manifest.json">
    <link rel="apple-touch-icon" href="<?= base_url() ?>public/assets/system_img/logo-192.png">
    <link rel="shortcut icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <!-- DataTables Bootstrap 5 -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- DataTables Buttons -->
    <link href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Add this in the head section if not already present -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Google Charts Library -->
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <!-- jQuery first - moved to head to ensure it's available -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Loading Spinner Script -->
    <script>
        // Show spinner when page starts loading
        document.addEventListener('DOMContentLoaded', function() {
            // Hide spinner when DOM is loaded
            setTimeout(function() {
                var spinner = document.getElementById('pageLoadingSpinner');
                if (spinner) {
                    spinner.classList.add('loaded');
                    // Remove from DOM after transition completes
                    setTimeout(function() {
                        spinner.style.display = 'none';
                    }, 300);
                }
            }, 500); // Small delay to ensure everything is loaded
        });

        // Show spinner when navigating to a new page
        document.addEventListener('click', function(e) {
            // Check if the clicked element is a link that navigates to a new page
            if (e.target.tagName === 'A' &&
                e.target.href &&
                !e.target.getAttribute('download') &&
                e.target.getAttribute('target') !== '_blank' &&
                !e.target.getAttribute('href').startsWith('#') &&
                !e.target.getAttribute('href').startsWith('javascript:')) {

                var spinner = document.getElementById('pageLoadingSpinner');
                if (spinner) {
                    spinner.classList.remove('loaded');
                    spinner.style.display = 'flex';
                }
            }
        });
    </script>

    <title><?= isset($title) ? $title . ' - ' : '' ?>GovPSS</title>

    <style>
        :root {
            --png-red: #CE1126;
            --png-black: #000000;
            --png-gold: #FCD116;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }

        /* DataTables fixes */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_processing,
        .dataTables_wrapper .dataTables_paginate {
            margin-bottom: 10px;
        }

        .dataTables_wrapper .dataTables_length select {
            min-width: 60px;
        }

        .gradient-bg {
            background: linear-gradient(135deg, rgba(206, 17, 38, 0.9), rgba(0, 0, 0, 0.9));
        }

        /* DataTables Custom Styling */
        .dataTables_wrapper .dt-buttons {
            margin-bottom: 10px;
        }

        table.dataTable thead th {
            position: relative;
            background-image: none !important;
        }

        table.dataTable thead th.sorting:after,
        table.dataTable thead th.sorting_asc:after,
        table.dataTable thead th.sorting_desc:after {
            position: absolute;
            right: 8px;
            display: block;
            font-family: 'Font Awesome 5 Free';
            opacity: 0.5;
            font-weight: 900;
        }

        table.dataTable thead th.sorting:after {
            content: "\f0dc";
            opacity: 0.2;
        }

        table.dataTable thead th.sorting_asc:after {
            content: "\f0de";
        }

        table.dataTable thead th.sorting_desc:after {
            content: "\f0dd";
        }

        /* Filter select styling */
        table.dataTable thead th select.form-select-sm {
            width: 100%;
            padding-right: 25px;
            margin-top: 5px;
            font-size: 0.75rem;
        }

        /* Loading Spinner Styles */
        .page-loading-spinner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s, visibility 0.3s;
        }

        .spinner-container {
            text-align: center;
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-top: 4px solid var(--png-red);
        }

        .spinner-logo {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        .spinner-border.text-danger {
            color: var(--png-red) !important;
        }

        .page-loading-spinner p {
            color: var(--png-red);
            font-weight: 500;
        }

        .page-loading-spinner.loaded {
            opacity: 0;
            visibility: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 250px;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            padding: 0;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.95), rgba(26, 26, 26, 0.95));
            transition: all 0.3s;
            z-index: 1040;
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        /* Add styles for the scrollable navigation */
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: var(--png-red) rgba(0, 0, 0, 0.2);
        }

        /* Webkit scrollbar styles */
        .sidebar-nav::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.2);
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background-color: var(--png-red);
            border-radius: 3px;
        }

        .sidebar-nav::-webkit-scrollbar-thumb:hover {
            background-color: #a00d1d;
        }

        .main-content {
            margin-left: 250px;
            transition: all 0.3s;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content.expanded {
            margin-left: 70px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.7) !important;
            padding: 0.8rem 1rem;
            transition: all 0.3s;
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }

        .nav-link:hover {
            color: var(--png-gold) !important;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: linear-gradient(90deg, var(--png-red), rgba(206, 17, 38, 0.8));
            color: white !important;
        }

        .nav-link i {
            width: 1.5rem;
            text-align: center;
            margin-right: 1rem;
            font-size: 1.1rem;
        }

        .nav-text {
            transition: opacity 0.3s;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            display: none;
        }

        /* Card Hover Effect */
        .hover-card {
            transition: all 0.3s;
        }

        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 .5rem 1rem rgba(206, 17, 38, 0.15);
        }

        /* Toggle Button */
        #sidebarToggle {
            position: absolute;
            right: -1rem;
            top: 1rem;
            z-index: 1050;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background-color: var(--png-red);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #sidebarToggle:hover {
            background-color: #a00d1d;
        }

        /* Mobile Toggle Button */
        .mobile-toggle {
            position: fixed;
            left: 0.5rem;
            top: 0.5rem;
            z-index: 1062;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--png-red);
            border: none;
            color: white;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .mobile-toggle:hover {
            background-color: #a00d1d;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                margin-left: -250px;
                transform: translateX(0);
                transition: all 0.3s ease-in-out;
                z-index: 1061;
                left: 0;
            }

            .sidebar.mobile-active {
                margin-left: 0;
                transform: translateX(0);
                left: 0;
            }

            .main-content {
                margin-left: 0 !important;
            }

            .mobile-toggle {
                display: flex;
                transition: all 0.3s ease-in-out;
            }

            #sidebarToggle {
                display: none;
            }

            .sidebar.mobile-active ~ .mobile-toggle {
                left: 250px;
            }
        }

        /* Bootstrap 5 Custom Styles */
        .btn-primary {
            background-color: var(--png-red);
            border-color: var(--png-red);
        }

        .btn-primary:hover {
            background-color: #a00d1d;
            border-color: #a00d1d;
        }

        .btn-outline-primary {
            color: var(--png-red);
            border-color: var(--png-red);
        }

        .btn-outline-primary:hover {
            background-color: var(--png-red);
            border-color: var(--png-red);
            color: white;
        }

        .text-primary {
            color: var(--png-red) !important;
        }

        .bg-primary {
            background-color: var(--png-red) !important;
        }

        .border-primary {
            border-color: var(--png-red) !important;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--png-red);
            box-shadow: 0 0 0 0.25rem rgba(206, 17, 38, 0.25);
        }

        .dropdown-item.active, .dropdown-item:active {
            background-color: var(--png-red);
        }

        .page-link {
            color: var(--png-red);
        }

        .page-item.active .page-link {
            background-color: var(--png-red);
            border-color: var(--png-red);
        }
    </style>

    <!-- PWA Support - Temporarily disabled to fix icon errors -->
    <!--
    <link rel="manifest" href="<?= base_url() ?>/manifest.json">
    <meta name="theme-color" content="#CE1126">
    <link rel="apple-touch-icon" href="<?= base_url() ?>/public/assets/icons/icon-144x144.png">
    -->

    <!-- PWA Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('<?= base_url() ?>public/service-worker.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    }).catch(function(error) {
                        console.log('ServiceWorker registration failed: ', error);
                    });
            });
        }

        // Suppress manifest-related console errors
        console.error = (function(originalError) {
            return function(message) {
                // Filter out manifest-related errors
                if (typeof message === 'string' &&
                   (message.includes('Manifest') ||
                    message.includes('icon') ||
                    message.includes('ServiceWorker'))) {
                    return;
                }
                originalError.apply(console, arguments);
            };
        })(console.error);
    </script>
</head>

<body>
    <!-- Page Loading Spinner -->
    <div id="pageLoadingSpinner" class="page-loading-spinner">
        <div class="spinner-container">
            <div class="spinner-logo mb-3">
                <img src="<?= base_url() ?>public/assets/system_img/system-logo.png" alt="Logo" width="60" height="60">
            </div>
            <div class="spinner-border text-danger" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-danger fw-medium">Loading...</p>
        </div>
    </div>

    <?php if(session()->get('logged_in')): ?>
    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" id="mobileToggle">
        <i class="fas fa-ellipsis-v fs-5"></i>
    </button>

    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <!-- Toggle Button -->
            <button id="sidebarToggle" class="shadow-sm">
                <i class="fas fa-chevron-left"></i>
            </button>

            <!-- Logo Section -->
            <div class="p-4 border-bottom border-dark">
                <a href="<?= base_url() ?>dashboard" class="d-flex align-items-center text-decoration-none">
                    <img src="<?= base_url() ?>public/assets/system_img/system-logo.png" alt="Logo" class="me-3" style="width: 40px; height: 40px;">
                    <span class="nav-text h5 mb-0 text-warning fw-bold">GovPSS</span>
                </a>
            </div>

            <!-- User Profile -->
            <div class="p-4 border-bottom border-dark">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle bg-gradient me-3 d-flex align-items-center justify-content-center"
                         style="width: 40px; height: 40px; background: linear-gradient(45deg, var(--png-red), var(--png-gold))">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="nav-text">
                        <p class="mb-0 small fw-semibold text-warning"><?= session()->get('name') ?></p>
                        <p class="mb-0 small text-muted"><?= ucfirst(session()->get('role')) ?></p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="sidebar-nav">
                <div class="nav flex-column py-3">
                    <a href="<?= base_url('dashboard') ?>" class="nav-link <?= ($menu == 'dashboard') ? 'active' : '' ?>">
                        <i class="fas fa-home"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="<?= base_url('letters') ?>" class="nav-link <?= ($menu == 'letters') ? 'active' : '' ?>">
                        <i class="fas fa-envelope"></i>
                        <span class="nav-text">Letters Management</span>
                    </a>
                    <a href="<?= base_url('employees') ?>" class="nav-link <?= ($menu == 'employees') ? 'active' : '' ?>">
                        <i class="fas fa-user-tie"></i>
                        <span class="nav-text">Employees</span>
                    </a>
                    <a href="<?= base_url('leave') ?>" class="nav-link <?= ($menu == 'leave') ? 'active' : '' ?>">
                        <i class="fas fa-calendar-alt"></i>
                        <span class="nav-text">Manage Leave</span>
                    </a>
                    <a href="<?= base_url('groups') ?>" class="nav-link <?= ($menu == 'positions') ? 'active' : '' ?>">
                        <i class="fas fa-briefcase"></i>
                        <span class="nav-text">Manage Positions</span>
                    </a>
                    <a href="<?= base_url('appointments') ?>" class="nav-link <?= ($menu == 'appointments') ? 'active' : '' ?>">
                        <i class="fas fa-file-signature"></i>
                        <span class="nav-text">Manage Appointments</span>
                    </a>
                    <a href="<?= base_url('payslips') ?>" class="nav-link <?= ($menu == 'payslips') ? 'active' : '' ?>">
                        <i class="fas fa-receipt"></i>
                        <span class="nav-text">Manage Payslips</span>
                    </a>


                    <!-- Reports Section -->
                    <div class="nav-text px-3 py-2 mt-3">
                        <small class="text-uppercase text-warning fw-semibold fst-italic">Reports</small>
                    </div>
                    <!-- <a href="<?= base_url('reports/establishment') ?>" class="nav-link <?= ($menu == 'establishment') ? 'active' : '' ?>">
                        <i class="fas fa-clipboard-list"></i>
                        <span class="nav-text">Establishment Register</span>
                    </a> -->
                    <a href="<?= base_url('reports/salary') ?>" class="nav-link <?= ($menu == 'salary') ? 'active' : '' ?>">
                        <i class="fas fa-chart-line"></i>
                        <span class="nav-text">Salary Analysis</span>
                    </a>

                    <!-- Settings -->
                    <div class="nav-text px-3 py-2 mt-3">
                        <small class="text-uppercase text-warning fw-semibold fst-italic">Settings</small>
                    </div>
                    <a href="<?= base_url('settings/organization') ?>" class="nav-link <?= ($menu == 'org_settings') ? 'active' : '' ?>">
                        <i class="fas fa-cog"></i>
                        <span class="nav-text">Org. Settings</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div id="mainContent" class="main-content">
            <!-- Header -->
            <header class="bg-white shadow-sm sticky-top">
                <div class="container-fluid px-4 py-3">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="h4 mb-0 text-gray-800 fw-bold"><?= $title ?></h1>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex align-items-center gap-4">
                                <button class="btn btn-link position-relative p-0">
                                    <i class="fas fa-bell text-muted fs-5"></i>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                        3
                                    </span>
                                </button>
                                <div class="dropdown">
                                    <button class="btn btn-link text-decoration-none text-muted dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-user-circle me-2"></i>
                                        <?= session()->get('name') ?>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="<?= base_url('profile') ?>">Profile</a></li>
                                        <li><a class="dropdown-item" href="<?= base_url('settings') ?>">Settings</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="<?= base_url('logout') ?>">Logout</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="container-fluid p-4">
                <?php echo $this->renderSection('content') ?>
            </div>

            <!-- Footer -->
            <footer class="bg-white border-top mt-auto">
                <div class="container-fluid px-4 py-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <img src="<?= base_url() ?>public/assets/system_img/dakoii-logo.png" alt="Dakoii" height="24" class="me-2">
                            <span class="small text-muted">
                                &copy; 2024
                                <a href="https://www.dakoiims.com" class="text-decoration-none text-danger hover-text-warning">
                                    Dakoii Systems
                                </a>
                            </span>
                        </div>
                        <div class="small text-muted">
                            <?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    <?php else: ?>
    <!-- Public Layout -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>public/assets/system_img/system-logo.png" alt="Logo" height="40" class="me-3">
                <span class="h4 mb-0">GovPSS</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a href="<?= base_url() ?>" class="nav-link <?= ($menu == 'home') ? 'active' : '' ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('login') ?>" class="nav-link <?= ($menu == 'login') ? 'active' : '' ?>">Login</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('about') ?>" class="nav-link <?= ($menu == 'about') ? 'active' : '' ?>">About</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="gradient-bg py-3">
        <h4 class="text-center text-white mb-0">GOVERNMENT PUBLIC SERVANTS SUPPORT SYSTEM</h4>
    </div>

    <?php echo $this->renderSection('content') ?>

    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <div class="mb-3">
                <img src="<?= base_url() ?>public/assets/system_img/dakoii-logo.png" alt="Dakoii" height="32">
            </div>
            <p class="small mb-1">&copy; 2024 <a href="https://www.dakoiims.com" class="text-warning text-decoration-none">Dakoii Systems</a></p>
            <p class="small mb-0"><?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></p>
        </div>
    </footer>
    <?php endif; ?>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Extensions -->
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.colVis.min.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Add this just before the closing body tag if not already present -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <!-- SweetAlert Messages -->
    <script>
        <?php if (session()->getFlashdata('success')): ?>
            Swal.fire({
                title: 'Success!',
                text: '<?= session()->getFlashdata('success') ?>',
                icon: 'success',
                confirmButtonColor: '#CE1126',
                timer: 2000,
                timerProgressBar: true,
                showConfirmButton: false
            });
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            Swal.fire({
                title: 'Error!',
                text: '<?= session()->getFlashdata('error') ?>',
                icon: 'error',
                confirmButtonColor: '#CE1126',
                timer: 2000,
                timerProgressBar: true,
                showConfirmButton: false
            });
        <?php endif; ?>
    </script>

    <!-- Sidebar Toggle Script -->
    <script>
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileToggle = document.getElementById('mobileToggle');
        const toggleIcon = sidebarToggle.querySelector('i');

        function toggleSidebar() {
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('mobile-active');
                toggleIcon.classList.toggle('fa-chevron-right');
                toggleIcon.classList.toggle('fa-chevron-left');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
                toggleIcon.classList.toggle('fa-chevron-right');
                toggleIcon.classList.toggle('fa-chevron-left');
            }
        }

        function setupResponsive() {
            if (window.innerWidth <= 768) {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
                sidebar.classList.remove('mobile-active');
                toggleIcon.classList.remove('fa-chevron-right');
                toggleIcon.classList.add('fa-chevron-left');
            } else {
                sidebar.classList.remove('mobile-active');
                toggleIcon.classList.remove('fa-chevron-right');
                toggleIcon.classList.add('fa-chevron-left');
            }
        }

        // Add mobile toggle button event listener
        mobileToggle.addEventListener('click', toggleSidebar);
        sidebarToggle.addEventListener('click', toggleSidebar);
        window.addEventListener('resize', setupResponsive);
        window.addEventListener('load', setupResponsive);

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !sidebarToggle.contains(event.target) &&
                !mobileToggle.contains(event.target) &&
                sidebar.classList.contains('mobile-active')) {
                toggleSidebar();
            }
        });
    </script>

    <!-- Loading Spinner Form Submission Handler -->
    <script>
        // Show spinner on form submissions
        document.addEventListener('DOMContentLoaded', function() {
            var forms = document.querySelectorAll('form:not([data-no-spinner])');
            forms.forEach(function(form) {
                form.addEventListener('submit', function() {
                    var spinner = document.getElementById('pageLoadingSpinner');
                    if (spinner) {
                        spinner.classList.remove('loaded');
                        spinner.style.display = 'flex';
                    }
                });
            });

            // Add spinner for AJAX requests
            $(document).ajaxStart(function() {
                var spinner = document.getElementById('pageLoadingSpinner');
                if (spinner) {
                    spinner.classList.remove('loaded');
                    spinner.style.display = 'flex';
                }
            });

            $(document).ajaxStop(function() {
                var spinner = document.getElementById('pageLoadingSpinner');
                if (spinner) {
                    spinner.classList.add('loaded');
                    setTimeout(function() {
                        spinner.style.display = 'none';
                    }, 300);
                }
            });
        });
    </script>
</body>
</html>