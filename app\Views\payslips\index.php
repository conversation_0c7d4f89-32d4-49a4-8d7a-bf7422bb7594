<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <h1 class="mt-4">Payslips Management</h1>

    <?php if(session()->has('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if(session()->has('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-receipt me-1"></i>
                Payslips
            </div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                <i class="fas fa-upload"></i> Upload Payslip
            </button>
        </div>
        <div class="card-body">
            <table id="payslipsTable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>Pay Number</th>
                        <th>Pay Date</th>
                        <th>Upload Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($payslips as $payslip): ?>
                    <tr>
                        <td><?= esc($payslip['pay_no']) ?></td>
                        <td><?= date('d M Y', strtotime($payslip['pay_date'])) ?></td>
                        <td><?= date('d M Y H:i', strtotime($payslip['created_at'])) ?></td>
                        <td>
                            <a href="<?= base_url('payslips/download/' . $payslip['id']) ?>" 
                               class="btn btn-sm btn-success" title="Download">
                                <i class="fas fa-download"></i>
                            </a>
                            <a href="<?= base_url('payslips/delete/' . $payslip['id']) ?>" 
                               class="btn btn-sm btn-danger"
                               onclick="return confirm('Are you sure you want to delete this payslip?')" 
                               title="Delete">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Payslip</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('payslips/store') ?>" method="post" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Pay Number</label>
                        <input type="text" class="form-control <?= session('validation.pay_no') ? 'is-invalid' : '' ?>" 
                               name="pay_no" value="<?= old('pay_no') ?>" required>
                        <?php if (session('validation.pay_no')): ?>
                            <div class="invalid-feedback">
                                <?= session('validation.pay_no') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Pay Date</label>
                        <input type="date" class="form-control <?= session('validation.pay_date') ? 'is-invalid' : '' ?>" 
                               name="pay_date" value="<?= old('pay_date') ?>" required>
                        <?php if (session('validation.pay_date')): ?>
                            <div class="invalid-feedback">
                                <?= session('validation.pay_date') ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Payslip File (PDF only, max 5MB)</label>
                        <input type="file" class="form-control <?= session('validation.payslip_file') ? 'is-invalid' : '' ?>" 
                               name="payslip_file" accept=".pdf" required>
                        <?php if (session('validation.payslip_file')): ?>
                            <div class="invalid-feedback">
                                <?= session('validation.payslip_file') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#payslipsTable').DataTable({
        order: [[2, 'desc']] // Sort by upload date by default
    });

    // Show upload modal if there are validation errors
    <?php if (session('validation')): ?>
        $('#uploadModal').modal('show');
    <?php endif; ?>
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 