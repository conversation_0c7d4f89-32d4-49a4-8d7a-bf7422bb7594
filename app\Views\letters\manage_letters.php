<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <?php if(session()->has('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if(session()->has('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <!-- Total Letters -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Total Letters</p>
                            <h3 class="fw-bold mb-3"><?= $stats['total'] ?></h3>
                        </div>
                        <div class="bg-info bg-opacity-10 rounded p-3">
                            <i class="fas fa-envelope text-info fs-4"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #0dcaf0, #0d6efd);"></div>
                </div>
            </div>
        </div>

        <!-- Pending Letters -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Pending</p>
                            <h3 class="fw-bold mb-3"><?= $stats['pending'] ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 rounded p-3">
                            <i class="fas fa-clock text-warning fs-4"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #ffc107, #fd7e14);"></div>
                </div>
            </div>
        </div>

        <!-- Approved Letters -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Approved</p>
                            <h3 class="fw-bold mb-3"><?= $stats['approved'] ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 rounded p-3">
                            <i class="fas fa-check text-success fs-4"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #198754, #20c997);"></div>
                </div>
            </div>
        </div>

        <!-- Rejected Letters -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Rejected</p>
                            <h3 class="fw-bold mb-3"><?= $stats['rejected'] ?></h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 rounded p-3">
                            <i class="fas fa-times text-danger fs-4"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #dc3545, #b02a37);"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Letters Table Card -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Manage Letters</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle table-striped datatable" id="lettersTable">
                    <thead class="table-light">
                        <tr>
                            <th>Subject</th>
                            <th>Type</th>
                            <th>Address To</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($letters as $letter): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                        <i class="fas fa-envelope text-muted"></i>
                                    </div>
                                    <span class="fw-medium"><?= esc($letter['subject']) ?></span>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info bg-opacity-10 text-info">
                                    <?= ucfirst($letter['letter_type']) ?>
                                </span>
                            </td>
                            <td><?= esc($letter['address_to']) ?></td>
                            <td>
                                <?php
                                $statusClass = '';
                                switch($letter['confirmation_status']) {
                                    case 'approved':
                                        $statusClass = 'bg-success text-success';
                                        break;
                                    case 'rejected':
                                        $statusClass = 'bg-danger text-danger';
                                        break;
                                    default:
                                        $statusClass = 'bg-warning text-warning';
                                        break;
                                }
                                ?>
                                <span class="badge bg-opacity-10 <?= $statusClass ?>">
                                    <?= ucfirst($letter['confirmation_status']) ?>
                                </span>
                            </td>
                            <td><?= date('M d, Y', strtotime($letter['created_at'])) ?></td>
                            <td>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewLetterModal<?= $letter['id'] ?>">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- View Letter Modals -->
<?php foreach ($letters as $letter): ?>
<div class="modal fade" id="viewLetterModal<?= $letter['id'] ?>" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Letter Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <!-- Letter Type -->
                    <div class="col-md-6">
                        <label class="form-label text-muted small">Letter Type</label>
                        <p class="mb-0">
                            <span class="badge bg-info bg-opacity-10 text-info">
                                <?= ucfirst($letter['letter_type']) ?>
                            </span>
                        </p>
                    </div>
                    
                    <!-- Status -->
                    <div class="col-md-6">
                        <label class="form-label text-muted small">Status</label>
                        <p class="mb-0">
                            <?php
                            $statusClass = '';
                            switch($letter['confirmation_status']) {
                                case 'approved':
                                    $statusClass = 'bg-success text-success';
                                    break;
                                case 'rejected':
                                    $statusClass = 'bg-danger text-danger';
                                    break;
                                default:
                                    $statusClass = 'bg-warning text-warning';
                                    break;
                            }
                            ?>
                            <span class="badge bg-opacity-10 <?= $statusClass ?>">
                                <?= ucfirst($letter['confirmation_status']) ?>
                            </span>
                        </p>
                    </div>

                    <!-- Subject -->
                    <div class="col-12">
                        <label class="form-label text-muted small">Subject</label>
                        <p class="mb-0 fw-medium"><?= esc($letter['subject']) ?></p>
                    </div>

                    <!-- Address To -->
                    <div class="col-12">
                        <label class="form-label text-muted small">Address To</label>
                        <p class="mb-0"><?= nl2br(esc($letter['address_to'])) ?></p>
                    </div>

                    <!-- Content -->
                    <div class="col-12">
                        <label class="form-label text-muted small">Content</label>
                        <div class="bg-light rounded p-3">
                            <?= nl2br(esc($letter['content'])) ?>
                        </div>
                    </div>

                    <!-- Confirmation Details -->
                    <?php if ($letter['confirmation_status'] !== 'pending'): ?>
                    <div class="col-12">
                        <label class="form-label text-muted small">Confirmation Details</label>
                        <div class="bg-light rounded p-3">
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <small class="text-muted">Confirmed By:</small>
                                    <p class="mb-1"><?= esc($letter['confirmed_by_email']) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">Confirmed At:</small>
                                    <p class="mb-1"><?= date('M d, Y h:i A', strtotime($letter['confirmed_at'])) ?></p>
                                </div>
                                <?php if (!empty($letter['confirmation_remarks'])): ?>
                                <div class="col-12">
                                    <small class="text-muted">Remarks:</small>
                                    <p class="mb-0"><?= nl2br(esc($letter['confirmation_remarks'])) ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Created By -->
                    <div class="col-12">
                        <label class="form-label text-muted small">Created By</label>
                        <div class="d-flex align-items-center">
                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                <i class="fas fa-user text-muted"></i>
                            </div>
                            <div>
                                <p class="mb-0 fw-medium">
                                    <?= $letter['created_by_name'] ? esc($letter['created_by_name']) : 'Unknown Employee' ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class="col-12">
                        <div class="d-flex justify-content-between text-muted small">
                            <span>Created: <?= date('M d, Y h:i A', strtotime($letter['created_at'])) ?></span>
                            <span>Last Updated: <?= date('M d, Y h:i A', strtotime($letter['updated_at'])) ?></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>



<script>
// Simple DataTable initialization
$(document).ready(function() {
    console.log('Document ready - initializing DataTable');
    try {
        $('#lettersTable').DataTable({
            "paging": true,
            "ordering": true,
            "info": true,
            "searching": true
        });
        console.log('DataTable initialized successfully');
    } catch (error) {
        console.log('Error initializing DataTable:', error);
    }
});
</script> 

<?= $this->endSection() ?>