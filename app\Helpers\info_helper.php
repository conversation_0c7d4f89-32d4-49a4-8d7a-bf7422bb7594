<?php

function datetimeforms($date)
{
    if ($date == "0000-00-00") {
        echo "No Date Set";
    } else {
        if (!empty($date)) {
            $dtime = new DateTime($date);
            print $dtime->format("d M Y H:ia");
        } else {
            echo "-";
        }
    }
}

function dateforms($date)
{
    if ($date == "0000-00-00") {
        echo "No Date Set";
    } else {

        if (!empty($date)) {
            $dtime = new DateTime($date);

            print $dtime->format("d M Y");
        } else {
            echo "-";
        }
    }
}

function getAge($date)
{
    if (!empty($date)) {
        $dateOfBirth = $date;
        $today = date("Y-m-d");
        $diff = date_diff(date_create($dateOfBirth), date_create($today));
        $age = $diff->format('%y');
        if ($age == date("Y")) {
            echo "--";
        } else {
            return $age;
        }
    } else {
        echo "-";
    }
}

function getDateAgo($date)
{
    if (!empty($date)) {
        $dateAgo = round((strtotime(date("Y-m-d H:i:s")) - strtotime($date)) / 86400);
        echo $dateAgo;
    } else {
        echo "-";
    }
}

function minstoexpire($date){
     //expire date count down
     $future_date = new DateTime($date);
     $current_date = new DateTime();

     // calculate the time difference between the two dates
     $time_diff = $future_date->diff($current_date);

     // extract the remaining days, hours, minutes, and seconds from the time difference
     $days = $time_diff->days;
     $hours = $time_diff->h;
     $minutes = $time_diff->i;
     $seconds = $time_diff->s;

     return $minutes;
     // output the remaining time

}

function imgcheck($img)
{
    $default_img = "public/assets/system_img/no-img.jpg";

    if (empty($img)) {
        echo base_url() . $default_img;
    } else {
        // Check if the file exists
        $file_path = ROOTPATH . $img;
        if (file_exists($file_path)) {
            echo base_url() . $img;
        } else {
            // If file doesn't exist, return the default image
            echo base_url() . $default_img;
        }
    }
}

function getfileExtension($filepath){

    if(!empty($filepath)){
        echo $fileExt = pathinfo($filepath,PATHINFO_EXTENSION);
    }else{
        echo "No File";
    }

}


function removeCommaWithEmptySpace($string) {
    $result = str_replace(', ', ' ', $string);
    $result = str_replace(',,', ' ', $result);
    return $result;
  }

  function calculate_age($dob)
  {
      return date_diff(date_create($dob), date_create('today'))->y;
  }

  /**
   * Get district name by district ID
   *
   * @param int|null $district_id
   * @return string
   */
  function get_district_name($district_id)
  {
      if (!$district_id) {
          return 'No District Assigned';
      }

      $db = \Config\Database::connect();
      $builder = $db->table('districts');
      $district = $builder->where('id', $district_id)->get()->getRowArray();

      return $district ? $district['name'] : 'Unknown District';
  }

