<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;
use CodeIgniter\Controller;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * AJAX_Controller Class
 * 
 * A base controller that disables the debug toolbar and provides
 * helper methods for AJAX responses.
 */
class AJAX_Controller extends Controller
{
    use ResponseTrait;
    
    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Disable debug toolbar by setting environment variable
        putenv('CI_DEBUG=0');
        
        // Initialize parent controller
        parent::initController($request, $response, $logger);
        
        // Set timezone (if needed)
        date_default_timezone_set('Pacific/Port_Moresby');
    }
    
    /**
     * Return a JSON success response
     * 
     * @param mixed  $data    The data to return
     * @param string $message Success message
     * @param int    $code    HTTP status code
     * 
     * @return ResponseInterface
     */
    protected function successResponse($data = [], string $message = 'Success', int $code = 200)
    {
        return $this->respond([
            'status' => true,
            'message' => $message,
            'data' => $data
        ], $code);
    }
    
    /**
     * Return a JSON error response
     * 
     * @param string $message Error message
     * @param int    $code    HTTP status code
     * @param array  $errors  Validation errors
     * 
     * @return ResponseInterface
     */
    protected function errorResponse(string $message = 'Error', int $code = 400, $errors = [])
    {
        return $this->respond([
            'status' => false,
            'message' => $message,
            'errors' => $errors
        ], $code);
    }
} 