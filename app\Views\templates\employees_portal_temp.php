<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Description" content="Government Public Servants Support System - Employee Portal" />
    <!-- Add CSRF Token -->
    <meta name="<?= csrf_token() ?>" content="<?= csrf_hash() ?>" />
    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <!-- Replace deprecated meta tag with web app manifest -->
    <!-- <meta name="apple-mobile-web-app-capable" content="yes"> -->
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="GovPSS">
    <meta name="theme-color" content="#CE1126">

    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">
    <!-- PWA Icons -->
    <link rel="apple-touch-icon" href="<?= base_url() ?>/public/assets/system_img/logo-192.png">
    <link rel="manifest" href="<?= base_url() ?>/manifest.json">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" crossorigin="anonymous">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts with font-display -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" crossorigin="anonymous" font-display="swap">
    <!-- DataTables Bootstrap 5 -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">

    <title><?= isset($title) ? $title . ' - ' : '' ?>GovPSS Employee Portal</title>

    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('<?= base_url() ?>/sw.js', {
                    scope: '<?= base_url() ?>/'
                })
                .then(function(registration) {
                    console.log('ServiceWorker registration successful');
                })
                .catch(function(err) {
                    console.log('ServiceWorker registration failed: ', err);
                });
            });
        }
    </script>

    <style>
        :root {
            --png-red: #CE1126;
            --png-black: #000000;
            --png-gold: #FCD116;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .gradient-bg {
            background: linear-gradient(135deg, rgba(206, 17, 38, 0.9), rgba(0, 0, 0, 0.9));
        }

        /* Card Hover Effect */
        .hover-card {
            transition: all 0.3s;
        }

        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 .5rem 1rem rgba(206, 17, 38, 0.15);
        }

        /* Bootstrap 5 Custom Styles */
        .btn-primary {
            background-color: var(--png-red);
            border-color: var(--png-red);
        }

        .btn-primary:hover {
            background-color: #a00d1d;
            border-color: #a00d1d;
        }

        .btn-outline-primary {
            color: var(--png-red);
            border-color: var(--png-red);
        }

        .btn-outline-primary:hover {
            background-color: var(--png-red);
            border-color: var(--png-red);
            color: white;
        }

        .text-primary {
            color: var(--png-red) !important;
        }

        .bg-primary {
            background-color: var(--png-red) !important;
        }

        .border-primary {
            border-color: var(--png-red) !important;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--png-red);
            box-shadow: 0 0 0 0.25rem rgba(206, 17, 38, 0.25);
        }

        .dropdown-item.active, .dropdown-item:active {
            background-color: var(--png-red);
        }

        /* Custom navbar styles */
        .navbar-custom {
            background: linear-gradient(90deg, var(--png-black), var(--png-red));
            padding: 1rem 0;
        }

        .navbar-custom .navbar-brand {
            color: var(--png-gold);
            font-weight: bold;
        }

        .navbar-custom .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
        }

        .navbar-custom .nav-link:hover {
            color: var(--png-gold) !important;
        }

        .navbar-custom .nav-link.active {
            color: var(--png-gold) !important;
            font-weight: 600;
        }

        /* Footer styles */
        .footer {
            margin-top: auto;
            background: linear-gradient(90deg, var(--png-black), var(--png-red));
            color: white;
            padding: 1.5rem 0;
        }
        /* Loading Spinner Styles */
        #loading-spinner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid var(--png-gold);
            border-top: 5px solid var(--png-red);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>

<body>
    <!-- Loading Spinner -->
    <div id="loading-spinner">
        <div class="spinner"></div>
    </div>
    <?php if(session()->get('isEmployeeLoggedIn')): ?>
    <!-- Employee Portal Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url('employee/dashboard') ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="Logo" height="40" class="me-2">
                <span>Employee Portal</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a href="<?= base_url('employee_portal/dashboard') ?>" class="nav-link">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('employee_portal/my_payslips') ?>" class="nav-link">Payslips</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('employee_portal/my_letters') ?>" class="nav-link">Letters</a>
                    </li>
                </ul>
                <div class="d-flex align-items-center">
                    <div class="dropdown">
                        <button class="btn btn-link text-decoration-none text-white dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <?= session()->get('name') ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?= base_url('employee_portal/my_profile') ?>">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="<?= base_url('employee_portal/logout') ?>">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-4">
        <?php echo $this->renderSection('content') ?>
    </div>

    <?php else: ?>
    <!-- Public Content -->
    <?php echo $this->renderSection('content') ?>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii" height="24" class="me-2">
                    <span class="small">
                        &copy; 2024
                        <a href="https://www.dakoiims.com" class="text-warning text-decoration-none">
                            Dakoii Systems
                        </a>
                    </span>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <span class="small"><?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></span>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery first -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <!-- Custom Scripts -->
    <?php echo $this->renderSection('scripts') ?>

    <script>
        // Show spinner on page load
        window.addEventListener('load', function() {
            document.getElementById('loading-spinner').style.display = 'none';
        });

        // Show spinner before page unload
        window.addEventListener('beforeunload', function() {
            document.getElementById('loading-spinner').style.display = 'flex';
        });

        // Show spinner on form submissions
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    document.getElementById('loading-spinner').style.display = 'flex';
                });
            });

            // Show spinner on navigation links
            const navLinks = document.querySelectorAll('a:not([target="_blank"])');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    document.getElementById('loading-spinner').style.display = 'flex';
                });
            });
        });
    </script>

    <!-- SweetAlert Messages -->
    <script>
        <?php if (session()->getFlashdata('success')): ?>
            Swal.fire({
                title: 'Success!',
                text: '<?= session()->getFlashdata('success') ?>',
                icon: 'success',
                confirmButtonColor: '#CE1126',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true,
                didOpen: () => {
                    // Hide spinner when alert shows
                    document.getElementById('loading-spinner').style.display = 'none';
                }
            });
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            Swal.fire({
                title: 'Error!',
                text: '<?= session()->getFlashdata('error') ?>',
                icon: 'error',
                confirmButtonColor: '#CE1126',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true,
                didOpen: () => {
                    // Hide spinner when alert shows
                    document.getElementById('loading-spinner').style.display = 'none';
                }
            });
        <?php endif; ?>
    </script>
</body>
</html>