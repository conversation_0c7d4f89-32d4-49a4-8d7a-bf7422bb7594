<?php

namespace App\Models;

use CodeIgniter\Model;

class PositionsModel extends Model
{
    protected $table = 'positions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'org_id',
        'group_id',
        'appointment_type', // 1: Substantive, 2: Acting, 3: Caretaker, 4: Probationary
        'employee_id',
        'position_code',
        'designation',
        'location',
        'appointment_date',
        'appointment_by',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
    ];

    protected $validationMessages = [];
    protected $skipValidation = true;

    /**
     * Create a new position
     */
    public function createPosition($data)
    {
        try {
            // Validate and insert the data
            if ($this->save($data)) {
                return [
                    'status' => true,
                    'message' => 'Position created successfully'
                ];
            }

            return [
                'status' => false,
                'message' => 'Failed to create position',
                'errors' => $this->errors()
            ];
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return [
                'status' => false,
                'message' => 'An error occurred while creating the position'
            ];
        }
    }

    /**
     * Update an existing position
     */
    public function updatePosition($id, $data)
    {
        try {
            // Check if position exists
            $position = $this->find($id);
            if (!$position) {
                return [
                    'status' => false,
                    'message' => 'Position not found'
                ];
            }

            // Add id to data for unique validation
            $data['id'] = $id;

            // Validate and update the data
            if ($this->save($data)) {
                return [
                    'status' => true,
                    'message' => 'Position updated successfully'
                ];
            }

            return [
                'status' => false,
                'message' => 'Failed to update position',
                'errors' => $this->errors()
            ];
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return [
                'status' => false,
                'message' => 'An error occurred while updating the position'
            ];
        }
    }

    /**
     * Delete a position
     */
    public function deletePosition($id)
    {
        try {
            // Check if position exists
            $position = $this->find($id);
            if (!$position) {
                return [
                    'status' => false,
                    'message' => 'Position not found'
                ];
            }

            // Delete the position
            if ($this->delete($id)) {
                return [
                    'status' => true,
                    'message' => 'Position deleted successfully'
                ];
            }

            return [
                'status' => false,
                'message' => 'Failed to delete position'
            ];
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return [
                'status' => false,
                'message' => 'An error occurred while deleting the position'
            ];
        }
    }

    /**
     * Get positions by group
     */
    public function getPositionsByGroup($group_id)
    {
        try {
            return [
                'status' => true,
                'data' => $this->where('group_id', $group_id)->findAll()
            ];
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return [
                'status' => false,
                'message' => 'An error occurred while fetching positions'
            ];
        }
    }
    
    /**
     * Get positions with appointment data
     * 
     * @param int $org_id Organization ID
     * @return array
     */
    public function getPositionsWithAppointments($org_id)
    {
        try {
            return $this->select('positions.*, groupings.name as group_name, employees.fname, employees.lname')
                ->join('groupings', 'groupings.id = positions.group_id', 'left')
                ->join('employees', 'employees.emp_id = positions.employee_id', 'left')
                ->where('positions.org_id', $org_id)
                ->findAll();
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return [];
        }
    }
    
    /**
     * Get position with appointment data by ID
     * 
     * @param int $position_id Position ID
     * @param int $org_id Organization ID
     * @return array|null
     */
    public function getPositionWithAppointment($position_id, $org_id)
    {
        try {
            return $this->select('positions.*, groupings.name as group_name, employees.fname, employees.lname')
                ->join('groupings', 'groupings.id = positions.group_id', 'left')
                ->join('employees', 'employees.emp_id = positions.employee_id', 'left')
                ->where('positions.id', $position_id)
                ->where('positions.org_id', $org_id)
                ->first();
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return null;
        }
    }

    /**
     * Vacate a position by removing the employee assignment
     *
     * @param int $position_id The ID of the position to vacate
     * @return bool Success or failure of the operation
     */
    public function vacatePosition($position_id)
    {
        try {
            // Update the position to remove employee details, appointment type and date
            $data = [
                'employee_id' => null,
                'appointment_type' => null,
                'appointment_date' => null,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->update($position_id, $data);
        } catch (\Exception $e) {
            log_message('error', 'Error vacating position: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if employee is already appointed to another position
     *
     * @param string $employee_id The employee ID to check
     * @param int $exclude_position_id Position ID to exclude from the check (for current position)
     * @return array|null Returns the position data if found, null otherwise
     */
    public function isEmployeeAppointed($employee_id, $exclude_position_id = null)
    {
        $builder = $this->db->table('positions');
        $builder->select('positions.*, employees.fname, employees.lname, employees.fileno');
        $builder->join('employees', 'employees.emp_id = positions.employee_id', 'left');
        $builder->where('positions.employee_id', $employee_id);
        
        // Exclude the current position if provided
        if ($exclude_position_id) {
            $builder->where('positions.id !=', $exclude_position_id);
        }
        
        $result = $builder->get()->getRowArray();
        
        return $result;
    }
} 