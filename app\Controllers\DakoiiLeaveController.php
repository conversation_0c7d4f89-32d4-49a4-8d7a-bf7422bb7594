<?php

namespace App\Controllers;

use App\Models\AdxLeaveModel;

class DakoiiLeaveController extends BaseController
{
    protected $adxLeaveModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->adxLeaveModel = new AdxLeaveModel();
    }

    /**
     * Display list of leave types
     */
    public function index()
    {
        $data['title'] = "Leave Types Management";
        $data['menu'] = "leave_types";

        $data['leave_types'] = $this->adxLeaveModel->getActiveLeaveTypes();

        echo view('dakoii/dakoii_leave_index', $data);
    }

    /**
     * Show create leave type form
     */
    public function create()
    {
        $data['title'] = "Add Leave Type";
        $data['menu'] = "leave_types";

        echo view('dakoii/dakoii_leave_create', $data);
    }

    /**
     * Store new leave type
     */
    public function store()
    {
        $rules = [
            'code' => 'required|max_length[50]|is_unique[adx_leave.code]',
            'name' => 'required|max_length[100]',
            'remarks' => 'permit_empty'
        ];

        if ($this->validate($rules)) {
            $data = [
                'code' => strtoupper(trim($this->request->getPost('code'))),
                'name' => $this->request->getPost('name'),
                'remarks' => $this->request->getPost('remarks'),
                'created_by' => $this->session->get('user_id') ?? null
            ];

            $result = $this->adxLeaveModel->createLeaveType($data);

            if ($result) {
                session()->setFlashdata('success', 'Leave type "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add leave type. Code or name might already exist.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }

        return redirect()->to('dakoii-leave');
    }

    /**
     * Show edit leave type form
     */
    public function edit($id)
    {
        $leave_type = $this->adxLeaveModel->getLeaveTypeById($id);

        if (!$leave_type) {
            session()->setFlashdata('error', 'Leave type not found');
            return redirect()->to('dakoii-leave');
        }

        $data['title'] = "Edit Leave Type";
        $data['menu'] = "leave_types";
        $data['leave_type'] = $leave_type;

        echo view('dakoii/dakoii_leave_edit', $data);
    }

    /**
     * Update leave type
     */
    public function update($id)
    {
        $leave_type = $this->adxLeaveModel->getLeaveTypeById($id);

        if (!$leave_type) {
            session()->setFlashdata('error', 'Leave type not found');
            return redirect()->to('dakoii-leave');
        }

        $rules = [
            'code' => 'required|max_length[50]|is_unique[adx_leave.code,id,' . $id . ']',
            'name' => 'required|max_length[100]',
            'remarks' => 'permit_empty'
        ];

        if ($this->validate($rules)) {
            $data = [
                'code' => strtoupper(trim($this->request->getPost('code'))),
                'name' => $this->request->getPost('name'),
                'remarks' => $this->request->getPost('remarks'),
                'updated_by' => $this->session->get('user_id') ?? null
            ];

            $result = $this->adxLeaveModel->updateLeaveType($id, $data);

            if ($result['status']) {
                session()->setFlashdata('success', $result['message']);
            } else {
                session()->setFlashdata('error', $result['message']);
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }

        return redirect()->to('dakoii-leave');
    }

    /**
     * Delete leave type (soft delete)
     */
    public function delete($id)
    {
        $leave_type = $this->adxLeaveModel->getLeaveTypeById($id);

        if (!$leave_type) {
            session()->setFlashdata('error', 'Leave type not found');
            return redirect()->to('dakoii-leave');
        }

        $result = $this->adxLeaveModel->softDelete($id, $this->session->get('user_id') ?? null);

        if ($result) {
            session()->setFlashdata('success', 'Leave type "' . $leave_type['name'] . '" has been deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete leave type. Please try again.');
        }

        return redirect()->to('dakoii-leave');
    }

    /**
     * Restore soft deleted leave type
     */
    public function restore($id)
    {
        $leave_type = $this->adxLeaveModel->find($id);

        if (!$leave_type) {
            session()->setFlashdata('error', 'Leave type not found');
            return redirect()->to('dakoii-leave');
        }

        $result = $this->adxLeaveModel->restore($id);

        if ($result) {
            session()->setFlashdata('success', 'Leave type "' . $leave_type['name'] . '" has been restored successfully');
        } else {
            session()->setFlashdata('error', 'Failed to restore leave type. Please try again.');
        }

        return redirect()->to('dakoii-leave');
    }
}
