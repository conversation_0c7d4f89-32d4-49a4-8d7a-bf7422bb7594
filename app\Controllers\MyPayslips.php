<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PayslipsModel;
use Smalot\PdfParser\Parser;
use setasign\Fpdi\Tcpdf\Fpdi;

// Custom PDF class with header and footer
class CustomPDF extends Fpdi
{
    protected $orgName;
    protected $groupName;
    protected $designation;
    protected $baseUrl;
    protected $pay_no;

    public function setOrgDetails($orgName, $groupName, $designation, $baseUrl, $pay_no)
    {
        $this->orgName = $orgName;
        $this->groupName = $groupName;
        $this->designation = $designation;
        $this->baseUrl = $baseUrl;
        $this->pay_no = $pay_no;
    }

    public function Header()
    {
        if ($this->pageNo() == 1) { // Only show header on first page
            // Organization Name
            $this->SetFont('helvetica', 'B', 14);
            $this->SetXY(10, 10);
            $this->Cell(0, 7, $this->orgName, 0, 1, 'L');

            // Position Details
            $this->SetFont('helvetica', '', 10);
            $this->SetX(10);
            $this->Cell(0, 6, 'Group: ' . $this->groupName, 0, 1, 'L');
            $this->SetX(10);
            $this->Cell(0, 6, 'Position: ' . $this->designation, 0, 1, 'L');
            $this->SetX(10);
            $this->Cell(0, 6, 'Pay No: ' . $this->pay_no, 0, 1, 'L');

            // Add a line
            $this->Line(10, 35, 200, 35);

            // Add some space after header
            $this->Ln(10);
        }
    }

    public function Footer()
    {
        // Position at 15 mm from bottom
        $this->SetY(-15);
        // Set font
        $this->SetFont('helvetica', 'I', 8);
        // Set text color to gray
        $this->SetTextColor(128);
        // Add the footer text with base URL
        $this->Cell(0, 10, 'Downloaded from GovPSS System (' . $this->baseUrl . ') @ ' . date('d-m-Y H:i'), 0, false, 'C');
    }
}

class MyPayslips extends BaseController
{
    protected $payslipsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'text']);
        $this->payslipsModel = new PayslipsModel();
        $this->session = session();
    }

    public function view()
    {
        if (!$this->session->get('isEmployeeLoggedIn')) {
            return redirect()->to('/');
        }

        $data = [
            'title' => 'My Payslips',
        ];

        $emp_id = $this->session->get('emp_id');
        $org_id = $this->session->get('org_id');

        // Get employee details first
        $db = \Config\Database::connect();
        $employee = $db->table('employees')
            ->where('emp_id', $emp_id)
            ->get()
            ->getRowArray();

        if (!$employee) {
            return redirect()->to('/')->with('error', 'Employee not found');
        }

        // Get the first 3 payslips ordered by pay_date desc

        /*  $payslips = $db->table('payslips')
            ->where('org_id', $org_id)
            ->orderBy('pay_date', 'DESC')
            ->limit(3)
            ->get()
            ->getResultArray(); */

        $payslips = $this->payslipsModel
            //->where('org_id', $org_id)
            ->orderBy('pay_date', 'DESC')
            ->limit(3)
            ->find();
            
        $all_payslips = $this->payslipsModel
        //->where('org_id', $org_id)
        ->orderBy('pay_date','DESC')
        ->find();

        // Filter payslips by searching for employee file number in PDF content
        $matchingPayslips = [];
        foreach ($payslips as $payslip) {
            if (!file_exists($payslip['file_path'])) {
                continue;
            }

            try {
                // Create parser
                $parser = new \Smalot\PdfParser\Parser();

                // Parse PDF file
                $pdf = $parser->parseFile($payslip['file_path']);

                // Get text content
                $text = $pdf->getText();

                // Search for employee file number
                if (stripos($text, $employee['fileno']) !== false) {
                    $matchingPayslips[] = $payslip;
                }
            } catch (\Exception $e) {
                log_message('error', 'Error parsing PDF: ' . $e->getMessage());
                continue;
            }
        }

        $data['employee'] = $employee;
        $data['payslips'] = $matchingPayslips;
        $data['all_payslips'] = $all_payslips;

        return view('employee_portal/my_payslips', $data);
    }

    public function download($id)
    {
        if (!$this->session->get('isEmployeeLoggedIn')) {
            return redirect()->to('/');
        }

        $emp_id = $this->session->get('emp_id');

        try {
            // Get employee details first with organization and position info
            $db = \Config\Database::connect();
            $employee = $db->table('employees')
                ->select('employees.*, dakoii_org.name as org_name, positions.designation, groupings.name as group_name')
                ->join('dakoii_org', 'dakoii_org.id = employees.org_id', 'left')
                ->join('positions', 'positions.employee_id = employees.emp_id', 'left')
                ->join('groupings', 'groupings.id = positions.group_id', 'left')
                ->where('employees.emp_id', $emp_id)
                ->get()
                ->getRowArray();

            if (!$employee) {
                throw new \Exception('Employee not found');
            }

            // Get the payslip
            $payslip = $db->table('payslips')
                ->where('id', $id)
                ->get()
                ->getRowArray();

            if (!$payslip) {
                throw new \Exception('Payslip not found');
            }

            // Check if file exists
            if (!file_exists($payslip['file_path'])) {
                throw new \Exception('Payslip file not found at: ' . $payslip['file_path']);
            }

            // Find the page containing employee file number
            $parser = new Parser();
            $sourcePdf = $parser->parseFile($payslip['file_path']);
            $pages = $sourcePdf->getPages();

            $matchingPage = null;
            foreach ($pages as $pageNum => $page) {
                $text = $page->getText();
                if (stripos($text, $employee['fileno']) !== false) {
                    $matchingPage = $pageNum + 1;
                    break;
                }
            }

            if ($matchingPage === null) {
                throw new \Exception('Employee file number not found in payslip');
            }

            // Ensure temp directory exists with proper permissions
            $tempDir = WRITEPATH . 'temp';
            if (!is_dir($tempDir)) {
                if (!mkdir($tempDir, 0777, true)) {
                    throw new \Exception('Failed to create temp directory');
                }
                chmod($tempDir, 0777);
            }

            // Create temporary file with unique name
            $tempFile = $tempDir . DIRECTORY_SEPARATOR . 'payslip_' . uniqid() . '.pdf';

            // Create new PDF with just the matching page
            $pdf = new CustomPDF('P', 'mm', 'A4');
            $pdf->setPrintHeader(true);
            $pdf->setPrintFooter(true);

            // Set organization details for header
            $pdf->setOrgDetails(
                $employee['org_name'] ?? 'Organization Name',
                $employee['group_name'] ?? 'Not Assigned',
                $employee['designation'] ?? 'Not Assigned',
                base_url(),
                $payslip['pay_no']
            );

            // Set source file
            $pageCount = $pdf->setSourceFile($payslip['file_path']);

            // Add only the matching page
            $pdf->AddPage();
            $tplIdx = $pdf->importPage($matchingPage);
            $pdf->useTemplate($tplIdx, 10, 45); // Adjust Y position to account for header

            // Output PDF to string first
            $pdfContent = $pdf->Output($tempFile, 'F');

            // Verify the file was created
            if (!file_exists($tempFile)) {
                throw new \Exception('Failed to create temporary PDF file');
            }

            // Set proper file permissions
            chmod($tempFile, 0644);

            // Create response
            $response = $this->response
                ->download($tempFile, null)
                ->setFileName('payslip_' . $employee['fileno'] . '_' . $payslip['pay_no'] . '.pdf')
                ->setContentType('application/pdf');

            // Delete temp file after sending
            register_shutdown_function(function () use ($tempFile) {
                if (file_exists($tempFile)) {
                    unlink($tempFile);
                }
            });

            return $response;
        } catch (\Exception $e) {
            log_message('error', 'Error processing PDF: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return redirect()->to('employee_portal/my_payslips')
                ->with('error', 'Error processing payslip file: ' . $e->getMessage());
        }
    }

    public function getPayslipsByDateRange()
    {
        if (!$this->session->get('isEmployeeLoggedIn')) {
            return $this->response->setJSON(['error' => 'Not logged in']);
        }

        try {
            $emp_id = $this->session->get('emp_id');
            $org_id = $this->session->get('org_id');

            log_message('debug', 'Employee ID: ' . $emp_id . ', Org ID: ' . $org_id);

            // Get employee details
            $db = \Config\Database::connect();
            $employee = $db->table('employees')
                ->where('emp_id', $emp_id)
                ->get()
                ->getRowArray();

            if (!$employee) {
                log_message('error', 'Employee not found for ID: ' . $emp_id);
                return $this->response->setJSON(['error' => 'Employee not found']);
            }

            log_message('debug', 'Employee commence date: ' . $employee['commence_date']);

            // Get payslips from commence date to current date
            $payslips = $this->payslipsModel->getPayslipsByDateRange(
                $employee['commence_date'],
                date('Y-m-d'),
                $org_id
            );

            log_message('debug', 'Found ' . count($payslips) . ' payslips');

            if (empty($payslips)) {
                return $this->response->setJSON(['payslips' => []]);
            }

            return $this->response->setJSON(['payslips' => $payslips]);
        } catch (\Exception $e) {
            log_message('error', 'Error in getPayslipsByDateRange: ' . $e->getMessage());
            return $this->response->setJSON(['error' => 'Error retrieving payslips']);
        }
    }

    public function checkPayslip($id)
    {
        if (!$this->session->get('isEmployeeLoggedIn')) {
            return $this->response->setJSON(['error' => 'Not logged in']);
        }

        try {
            $emp_id = $this->session->get('emp_id');

            // Get employee details
            $db = \Config\Database::connect();
            $employee = $db->table('employees')
                ->where('emp_id', $emp_id)
                ->get()
                ->getRowArray();

            if (!$employee) {
                return $this->response->setJSON(['error' => 'Employee not found']);
            }

            // Get the payslip
            $payslip = $this->payslipsModel->find($id);
            if (!$payslip) {
                return $this->response->setJSON(['error' => 'Payslip not found']);
            }

            // Check if file exists
            if (!file_exists($payslip['file_path'])) {
                return $this->response->setJSON(['error' => 'Payslip file not found']);
            }

            // Parse PDF and check for file number
            $parser = new Parser();
            $pdf = $parser->parseFile($payslip['file_path']);
            $text = $pdf->getText();

            if (stripos($text, $employee['fileno']) !== false) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Payslip found',
                    'payslip_id' => $id
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'You have no payslip for this pay number'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error checking payslip: ' . $e->getMessage());
            return $this->response->setJSON(['error' => 'Error processing payslip']);
        }
    }
}
