<?php

namespace App\Controllers;

use App\Models\PayslipsModel;

class Payslips extends BaseController
{
    public $session;
    protected $payslipsModel;

    public function __construct()
    {
        // Load helpers
        helper(['form', 'url', 'session', 'filesystem']);
        
        // Initialize session
        $this->session = session();
        
        // Initialize model
        $this->payslipsModel = new PayslipsModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Payslips Management',
            'menu' => 'payslips',
            'payslips' => $this->payslipsModel->findAll()
        ];

        return view('payslips/index', $data);
    }

    public function store()
    {
        // Validate file upload
        $validationRules = [
            'pay_no' => [
                'label' => 'Pay Number',
                'rules' => 'required|is_unique[payslips.pay_no]',
                'errors' => [
                    'required' => 'Pay Number is required',
                    'is_unique' => 'This Pay Number already exists'
                ]
            ],
            'pay_date' => [
                'label' => 'Pay Date',
                'rules' => 'required',
                'errors' => [
                    'required' => 'Pay Date is required'
                ]
            ],
            'payslip_file' => [
                'label' => 'Payslip File',
                'rules' => 'uploaded[payslip_file]|mime_in[payslip_file,application/pdf]|max_size[payslip_file,5120]',
                'errors' => [
                    'uploaded' => 'Please select a file to upload',
                    'mime_in' => 'Only PDF files are allowed',
                    'max_size' => 'File size should not exceed 5MB'
                ]
            ]
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()
                ->with('error', 'Please check your input')
                ->with('validation', $this->validator->getErrors())
                ->withInput();
        }

        // Handle file upload
        $file = $this->request->getFile('payslip_file');
        if (!$file->isValid()) {
            return redirect()->back()
                ->with('error', 'Failed to upload file')
                ->withInput();
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'public/uploads/payslips';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        // Generate unique filename
        $fileName = $file->getRandomName();

        // Move file to upload directory
        if (!$file->move($uploadPath, $fileName)) {
            return redirect()->back()
                ->with('error', 'Failed to save file')
                ->withInput();
        }

        // Save payslip record
        $data = [
            'org_id' => $this->session->get('org_id'),
            'pay_no' => $this->request->getPost('pay_no'),
            'pay_date' => $this->request->getPost('pay_date'),
            'file_path' => 'public/uploads/payslips/' . $fileName,
            'created_by' => $this->session->get('user_id'),
            'updated_by' => $this->session->get('user_id')
        ];

        if (!$this->payslipsModel->insert($data)) {
            // Delete uploaded file if database insert fails
            unlink($uploadPath . '/' . $fileName);
            return redirect()->back()
                ->with('error', 'Failed to save payslip record')
                ->withInput();
        }

        return redirect()->back()
            ->with('success', 'Payslip uploaded successfully');
    }

    public function delete($id)
    {
        $payslip = $this->payslipsModel->find($id);
        if (!$payslip) {
            return redirect()->back()->with('error', 'Payslip not found');
        }

        // Delete file
        $filePath = FCPATH . $payslip['file_path'];
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        // Delete record
        if (!$this->payslipsModel->delete($id)) {
            return redirect()->back()->with('error', 'Failed to delete payslip');
        }

        return redirect()->back()->with('success', 'Payslip deleted successfully');
    }

    public function download($id)
    {
        $payslip = $this->payslipsModel->find($id);
        if (!$payslip) {
            return redirect()->back()->with('error', 'Payslip not found');
        }

        $filePath = FCPATH . $payslip['file_path'];
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'Payslip file not found');
        }

        return $this->response->download($filePath, null)
            ->setFileName('Payslip_' . $payslip['pay_no'] . '.pdf');
    }
} 