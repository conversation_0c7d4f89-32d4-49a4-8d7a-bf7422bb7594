<?= $this->extend('templates/employees_portal_temp') ?>

<?= $this->section('content') ?>
<div class="min-h-screen bg-gray-100">
    <!-- Top Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <span class="text-xl font-semibold">Employee Portal</span>
                </div>
                <div class="flex items-center">
                    <span class="mr-4">Welcome, <?= esc($employee['fname']) ?></span>
                    <a href="<?= base_url('employee_portal/logout') ?>" 
                       class="bg-png-red hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Employee Information Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <h2 class="card-title h4 mb-4">Personal Information</h2>
                <div class="row g-4">
                    <div class="col-md-6 col-lg-3">
                        <div class="p-3 border rounded bg-light">
                            <p class="text-muted mb-1">Full Name</p>
                            <p class="fw-semibold mb-0"><?= esc($employee['fname']) ?> <?= esc($employee['lname']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="p-3 border rounded bg-light">
                            <p class="text-muted mb-1">File Number</p>
                            <p class="fw-semibold mb-0"><?= esc($employee['fileno']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="p-3 border rounded bg-light">
                            <p class="text-muted mb-1">Email</p>
                            <p class="fw-semibold mb-0"><?= esc($employee['primary_email']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="p-3 border rounded bg-light">
                            <p class="text-muted mb-1">Phone</p>
                            <p class="fw-semibold mb-0"><?= esc($employee['phone']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row g-4">
            <!-- Payslips Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card hover-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0 bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-receipt text-primary fa-2x"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="card-title h5 mb-0">Payslips</h3>
                            </div>
                        </div>
                        <p class="card-text text-muted mb-3">Access and download your monthly payslips securely.</p>
                        <a href="<?= base_url('employee_portal/my_payslips') ?>" class="btn btn-primary">
                            <i class="fas fa-file-download me-2"></i>View Payslips
                        </a>
                    </div>
                </div>
            </div>

            <!-- Letters Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card hover-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0 bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-envelope text-primary fa-2x"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="card-title h5 mb-0">Letters</h3>
                            </div>
                        </div>
                        <p class="card-text text-muted mb-3">View and download your official letters and correspondence.</p>
                        <a href="<?= base_url('employee_portal/my_letters') ?>" class="btn btn-primary">
                            <i class="fas fa-envelope-open-text me-2"></i>View Letters
                        </a>
                    </div>
                </div>
            </div>

            <!-- Profile Card -->
            <div class="col-md-6 col-lg-4">
                <div class="card hover-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0 bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-user text-primary fa-2x"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="card-title h5 mb-0">Profile</h3>
                            </div>
                        </div>
                        <p class="card-text text-muted mb-3">View and manage your personal information and settings.</p>
                        <a href="<?= base_url('employee_portal/my_profile') ?>" class="btn btn-primary">
                            <i class="fas fa-user-cog me-2"></i>View Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card shadow-sm mt-4">
            <div class="card-body">
                <h2 class="card-title h4 mb-4">Recent Activity</h2>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Activity</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?= date('Y-m-d') ?></td>
                                <td>Logged into the system</td>
                                <td><span class="badge bg-success">Completed</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?> 