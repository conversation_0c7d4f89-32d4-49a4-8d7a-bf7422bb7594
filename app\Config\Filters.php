<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;
use CodeIgniter\Filters\CSRF;
use CodeIgniter\Filters\DebugToolbar;
use CodeIgniter\Filters\Honeypot;
use CodeIgniter\Filters\InvalidChars;
use CodeIgniter\Filters\SecureHeaders;

class Filters extends BaseConfig
{
    /**
     * Configures aliases for Filter classes to
     * make reading things nicer and simpler.
     */
    public array $aliases = [
        'csrf'          => CSRF::class,
        'toolbar'       => DebugToolbar::class,
        'honeypot'      => Honeypot::class,
        'invalidchars'  => InvalidChars::class,
        'secureheaders' => SecureHeaders::class,
        'auth'          => \App\Filters\Auth::class,
        'auth:employee' => \App\Filters\EmployeeAuth::class,
    ];

    /**
     * List of filter aliases that are always
     * applied before and after every request.
     */
    public array $globals = [
        'before' => [
            'csrf' => ['except' => [
                'api/*'
            ]],
            'auth' => [
                'except' => [
                    '/',
                    'login',
                    'about',
                    'assets/*',
                    'public/*',
                    'employee_portal/search',
                    'employee_portal/login',
                    'employee_portal/*',
                    'dakoii',
                    'dlogin',
                    'view_letter/*/*',
                    'process_letter/*/*',
                    'letters/view_pdf/*'
                ]
            ]
        ],
        'after' => [
            'toolbar',
        ],
    ];

    /**
     * List of filter aliases that works on a
     * particular HTTP method (GET, POST, etc.).
     */
    public array $methods = [];

    /**
     * List of filter aliases that should run on any
     * before or after URI patterns.
     */
    public array $filters = [
        'auth' => [
            'before' => [
                'dashboard/*',
                'users/*',
                'groups/*',
                'employees/*',
                'payslips/*',
                'positions/*',
                'ddash',
                'dakoii/*'
            ]
        ],
        'auth:employee' => [
            'before' => [
                'employee_portal/dashboard',
                'employee_portal/my_payslips',
                'employee_portal/my_payslips/*',
                'employee_portal/profile',
                'employee_portal/letters'
            ]
        ]
    ];
}
