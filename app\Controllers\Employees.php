<?php

namespace App\Controllers;

use App\Models\employeesModel;
use CodeIgniter\RESTful\ResourceController;

class Employees extends ResourceController
{
    protected $employeesModel;
    protected $db;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->employeesModel = new employeesModel();
        $this->db = \Config\Database::connect();
    }

    public function index()
    {
        $data = [
            'title' => 'Employees Management',
            'menu' => 'employees'
        ];
        return view('employees/index', $data);
    }

    // Get all employees for DataTable
    public function getEmployees()
    {
        // Get current user's org_id from session
        $org_id = session()->get('org_id');
        
        // Get employees for this organization only
        $employees = $this->employeesModel->where('org_id', $org_id)->findAll();
        
        return $this->response->setJSON([
            'data' => array_map(function($employee) {
                return [
                    'emp_id' => $employee['emp_id'],
                    'fileno' => $employee['fileno'],
                    'name' => $employee['fname'] . ' ' . $employee['lname'],
                    'gender' => $employee['gender'],
                    'phone' => $employee['phone'],
                    'email' => $employee['primary_email'],
                    'status' => $employee['status'],
                    'actions' => '<div class="btn-group">
                                    <button onclick="editEmployee('.$employee['emp_id'].')" class="btn btn-link text-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>'
                ];
            }, $employees)
        ]);
    }

    // Get single employee
    public function get($id)
    {
        $org_id = session()->get('org_id');
        $employee = $this->employeesModel->where('org_id', $org_id)->find($id);
        
        if (!$employee) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Employee not found'
            ])->setStatusCode(404);
        }
        
        return $this->response->setJSON($employee);
    }

    // Save new employee
    public function save()
    {
        $rules = [
            'fileno' => 'required|is_unique[employees.fileno]',
            'fname' => 'required',
            'lname' => 'required',
            'gender' => 'required',
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id');
        $data['org_id'] = session()->get('org_id');
        $data['status'] = 'active';
        // Set default password as file number
        $data['password'] = password_hash($data['fileno'], PASSWORD_DEFAULT);

        try {
            $this->employeesModel->insert($data);
            return $this->response->setJSON([
                'status' => true,
                'message' => 'Employee added successfully',
                'csrf_token' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Failed to add employee'
            ])->setStatusCode(500);
        }
    }

    // Reset employee password
    public function resetPassword($id = null)
    {
        if ($id === null) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Invalid employee ID',
                'csrf_hash' => csrf_hash()
            ])->setStatusCode(400);
        }

        // Verify employee belongs to current organization
        $org_id = session()->get('org_id');
        $employee = $this->employeesModel->where('org_id', $org_id)->find($id);
        
        if (!$employee) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Employee not found',
                'csrf_hash' => csrf_hash()
            ])->setStatusCode(404);
        }

        try {
            // Reset password to file number
            $this->employeesModel->update($id, [
                'password' => password_hash($employee['fileno'], PASSWORD_DEFAULT),
                'updated_by' => session()->get('user_id')
            ]);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Password reset successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Failed to reset password',
                'csrf_hash' => csrf_hash()
            ])->setStatusCode(500);
        }
    }

    // Update employee
    public function update($id = null)
    {
        if ($id === null) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Invalid employee ID'
            ])->setStatusCode(400);
        }

        // Verify employee belongs to current organization
        $org_id = session()->get('org_id');
        $employee = $this->employeesModel->where('org_id', $org_id)->find($id);
        
        if (!$employee) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Employee not found'
            ])->setStatusCode(404);
        }

        $rules = [
            'fileno' => 'required|is_unique[employees.fileno,emp_id,'.$id.']',
            'fname' => 'required',
            'lname' => 'required',
            'gender' => 'required',
            'primary_email' => 'permit_empty|valid_email',
            'phone' => 'permit_empty',
            'status' => 'required|in_list[active,inactive]'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ])->setStatusCode(400);
        }

        $data = $this->request->getPost();
        $data['updated_by'] = session()->get('user_id');
        $data['org_id'] = $org_id; // Ensure org_id is set

        try {
            $this->employeesModel->update($id, $data);
            return $this->response->setJSON([
                'status' => true,
                'message' => 'Employee updated successfully',
                'csrf_token' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Failed to update employee'
            ])->setStatusCode(500);
        }
    }

    // Import employees from CSV
    public function import()
    {
        // Check if file was uploaded
        if (!$this->request->getFile('csvFile')) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'No file was uploaded'
            ])->setStatusCode(400);
        }

        $file = $this->request->getFile('csvFile');

        // Check if it's a valid CSV file
        if ($file->getClientMimeType() !== 'text/csv') {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Please upload a valid CSV file'
            ])->setStatusCode(400);
        }

        // Read CSV file
        $handle = fopen($file->getTempName(), 'r');
        if ($handle === false) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Unable to read the CSV file'
            ])->setStatusCode(400);
        }

        $org_id = session()->get('org_id');
        $user_id = session()->get('user_id');
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        $row = 1;

        try {
            // Start transaction
            $this->db->transStart();

            while (($data = fgetcsv($handle)) !== false) {
                // Skip header row if exists
                if ($row === 1) {
                    $row++;
                    continue;
                }

                // Check if we have all required columns
                if (count($data) < 3) {
                    $errors[] = "Row $row: Missing required columns";
                    $errorCount++;
                    $row++;
                    continue;
                }

                $fileno = trim($data[0]);
                $fname = trim($data[1]);
                $lname = trim($data[2]);

                // Validate required fields
                if (empty($fileno) || empty($fname) || empty($lname)) {
                    $errors[] = "Row $row: Missing required fields";
                    $errorCount++;
                    $row++;
                    continue;
                }

                // Check if file number already exists
                if ($this->employeesModel->where('fileno', $fileno)->where('org_id', $org_id)->first()) {
                    $errors[] = "Row $row: File number '$fileno' already exists";
                    $errorCount++;
                    $row++;
                    continue;
                }

                // Prepare employee data
                $employeeData = [
                    'org_id' => $org_id,
                    'fileno' => $fileno,
                    'fname' => $fname,
                    'lname' => $lname,
                    'password' => password_hash($fileno, PASSWORD_DEFAULT), // Set default password as file number
                    'status' => 'active',
                    'created_by' => $user_id
                ];

                // Insert employee
                if ($this->employeesModel->insert($employeeData)) {
                    $successCount++;
                } else {
                    $errors[] = "Row $row: Failed to insert employee";
                    $errorCount++;
                }

                $row++;
            }

            // Complete transaction
            $this->db->transComplete();

            fclose($handle);

            if ($this->db->transStatus() === false) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Database transaction failed',
                    'errors' => $errors
                ])->setStatusCode(500);
            }

            $message = "Successfully imported $successCount employee(s).";
            if ($errorCount > 0) {
                $message .= " Failed to import $errorCount record(s).";
            }

            return $this->response->setJSON([
                'status' => true,
                'message' => $message,
                'errors' => $errors,
                'csrf_token' => csrf_hash()
            ]);

        } catch (\Exception $e) {
            if (isset($handle)) {
                fclose($handle);
            }
            
            return $this->response->setJSON([
                'status' => false,
                'message' => 'An error occurred while importing employees',
                'error' => $e->getMessage()
            ])->setStatusCode(500);
        }
    }
} 